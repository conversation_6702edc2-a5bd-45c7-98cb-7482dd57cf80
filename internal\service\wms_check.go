package service

import (
	"context"
	v1 "daisy-server/api/v1"
	"daisy-server/internal/model"
	"daisy-server/internal/repository"
	"daisy-server/pkg/pagination"
	"time"

	"github.com/jinzhu/copier"
)

type WmsCheckService interface {
	Create(ctx context.Context, req *v1.WmsCheckCreateParams) error
	Update(ctx context.Context, id uint, req *v1.WmsCheckUpdateParams) error
	Delete(ctx context.Context, id uint) error
	BatchDelete(ctx context.Context, ids []uint) error
	Get(ctx context.Context, id uint) (*v1.WmsCheckResponse, error)
	List(ctx context.Context, params *pagination.Params) (*pagination.Result, error)
}

func NewWmsCheckService(
	service *Service,
	wmsCheckRepository repository.WmsCheckRepository,
) WmsCheckService {
	return &wmsCheckService{
		Service:            service,
		wmsCheckRepository: wmsCheckRepository,
	}
}

type wmsCheckService struct {
	*Service
	wmsCheckRepository repository.WmsCheckRepository
}

// 盘点单相关方法实现
func (s *wmsCheckService) Create(ctx context.Context, req *v1.WmsCheckCreateParams) error {
	check := &model.WmsCheck{}
	if err := copier.Copy(check, req); err != nil {
		return err
	}

	// 获取当前用户信息
	user := ctx.Value("user").(*model.SysUser)

	// 租户ID
	check.TenantId = user.TenantId

	// 创建人
	if user.Nickname != "" {
		check.CreatedBy = user.Nickname
	} else {
		check.CreatedBy = user.Username
	}

	return s.tm.Transaction(ctx, func(ctx context.Context) error {
		if err := s.wmsCheckRepository.Create(ctx, check); err != nil {
			return err
		}
		return nil
	})
}

func (s *wmsCheckService) Update(ctx context.Context, id uint, req *v1.WmsCheckUpdateParams) error {
	check, err := s.wmsCheckRepository.Get(ctx, id)
	if err != nil {
		return err
	}

	// 获取当前用户信息
	user := ctx.Value("user").(*model.SysUser)

	// 过滤非本租户数据
	if check.TenantId != user.TenantId {
		return v1.ErrForbidden
	}

	if err := copier.Copy(check, req); err != nil {
		return err
	}

	// 更新人
	if user.Nickname != "" {
		check.UpdatedBy = user.Nickname
	} else {
		check.UpdatedBy = user.Username
	}

	return s.tm.Transaction(ctx, func(ctx context.Context) error {
		if err := s.wmsCheckRepository.Update(ctx, check); err != nil {
			return err
		}
		return nil
	})
}

func (s *wmsCheckService) Delete(ctx context.Context, id uint) error {
	// 获取当前用户信息
	user := ctx.Value("user").(*model.SysUser)

	// 是否是该租户数据
	check, err := s.wmsCheckRepository.Get(ctx, id)
	if err != nil {
		return err
	}
	if check.TenantId != user.TenantId {
		return v1.ErrForbidden
	}

	return s.tm.Transaction(ctx, func(ctx context.Context) error {
		if err := s.wmsCheckRepository.Delete(ctx, id); err != nil {
			return err
		}
		return nil
	})
}

func (s *wmsCheckService) BatchDelete(ctx context.Context, ids []uint) error {
	// 获取当前用户信息
	user := ctx.Value("user").(*model.SysUser)

	// 获取目标记录
	checks, err := s.wmsCheckRepository.GetByIds(ctx, ids)
	if err != nil {
		return err
	}

	// 过滤非本租户数据
	var newIds []uint
	for _, check := range checks {
		if check.TenantId == user.TenantId {
			newIds = append(newIds, check.ID)
		}
	}

	return s.tm.Transaction(ctx, func(ctx context.Context) error {
		if err := s.wmsCheckRepository.BatchDelete(ctx, newIds); err != nil {
			return err
		}
		return nil
	})
}

func (s *wmsCheckService) Get(ctx context.Context, id uint) (*v1.WmsCheckResponse, error) {
	check, err := s.wmsCheckRepository.Get(ctx, id)
	if err != nil {
		return nil, err
	}

	// 获取当前用户信息
	user := ctx.Value("user").(*model.SysUser)

	// 过滤非本租户数据
	if check.TenantId != user.TenantId {
		return nil, v1.ErrForbidden
	}

	response := &v1.WmsCheckResponse{}
	if err := copier.Copy(response, check); err != nil {
		return nil, err
	}

	response.CreatedAt = check.CreatedAt.Format(time.RFC3339)
	response.UpdatedAt = check.UpdatedAt.Format(time.RFC3339)

	return response, nil
}

func (s *wmsCheckService) List(ctx context.Context, params *pagination.Params) (*pagination.Result, error) {
	// 获取当前用户信息
	user := ctx.Value("user").(*model.SysUser)

	// 过滤非本租户数据
	params.AddFilter("tenant_id", user.TenantId)

	checks, total, err := s.wmsCheckRepository.List(ctx, params)
	if err != nil {
		return nil, err
	}

	// 将原始记录转换为API响应格式
	records := make([]*v1.WmsCheckResponse, 0, len(checks))
	for _, check := range checks {
		response := &v1.WmsCheckResponse{}
		if err := copier.Copy(response, check); err != nil {
			return nil, err
		}

		response.CreatedAt = check.CreatedAt.Format(time.RFC3339)
		response.UpdatedAt = check.UpdatedAt.Format(time.RFC3339)

		records = append(records, response)
	}

	return &pagination.Result{
		Records: records,
		Total:   total,
	}, nil
}
