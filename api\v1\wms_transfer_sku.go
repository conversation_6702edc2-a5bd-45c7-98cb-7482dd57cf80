package v1

import "gorm.io/datatypes"

type WmsTransferSkuCreateParams struct {
	TransferId  uint           `json:"transferId" binding:"required" example:"1"`
	ItemId      uint           `json:"itemId" binding:"required" example:"1"`
	SkuId       uint           `json:"skuId" binding:"required" example:"1"`
	Num         float64        `json:"num" binding:"min=0" example:"100.50"`
	FromAreaNum datatypes.JSON `json:"fromAreaNum" swaggertype:"object" example:"[{\"areaPath\":[1,2,3],\"num\":50}]"`
	ToAreaNum   datatypes.JSON `json:"toAreaNum" swaggertype:"object" example:"[{\"areaPath\":[1,2,3],\"num\":50}]"`
	Status      bool           `json:"status" example:"false"`
	TenantId    uint           `json:"tenantId" example:"1"`
	CreatedBy   string         `json:"createdBy" example:"管理员"`
	UpdatedBy   string         `json:"updatedBy" example:"管理员"`
}

type WmsTransferSkuUpdateParams struct {
	WmsTransferSkuCreateParams
}

type WmsTransferSkuResponse struct {
	ID          uint                 `json:"id"`
	TransferId  uint                 `json:"transferId"`
	ItemId      uint                 `json:"itemId"`
	SkuId       uint                 `json:"skuId"`
	Num         float64              `json:"num"`
	FromAreaNum datatypes.JSON       `json:"fromAreaNum"`
	ToAreaNum   datatypes.JSON       `json:"toAreaNum"`
	Status      bool                 `json:"status"`
	TenantId    uint                 `json:"tenantId"`
	CreatedBy   string               `json:"createdBy"`
	UpdatedBy   string               `json:"updatedBy"`
	CreatedAt   string               `json:"createdAt"`
	UpdatedAt   string               `json:"updatedAt"`
	Transfer    *WmsTransferResponse `json:"transfer,omitempty"` // 调拨单信息
	Item        *WmsItemResponse     `json:"item,omitempty"`     // 物料信息
	Sku         *WmsSkuResponse      `json:"sku,omitempty"`      // 规格信息
}
