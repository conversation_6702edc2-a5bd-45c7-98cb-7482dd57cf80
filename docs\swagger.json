{"swagger": "2.0", "info": {"description": "This is a sample server celler server.", "title": "Nunu Example API", "termsOfService": "http://swagger.io/terms/", "contact": {"name": "API Support", "url": "http://www.swagger.io/support", "email": "<EMAIL>"}, "license": {"name": "Apache 2.0", "url": "http://www.apache.org/licenses/LICENSE-2.0.html"}, "version": "1.0.0"}, "host": "localhost:8000", "paths": {"/auth/constant_routes": {"get": {"description": "获取系统常量路由", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["认证模块"], "summary": "获取常量路由", "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/daisy-server_api_v1.Response"}}}}}, "/auth/exist_route": {"get": {"description": "检查指定路由名称是否存在", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["认证模块"], "summary": "检查路由是否存在", "parameters": [{"type": "string", "description": "路由名称", "name": "routeName", "in": "query", "required": true}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/daisy-server_api_v1.Response"}}}}}, "/auth/login": {"post": {"description": "使用用户名密码登录系统", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["认证模块"], "summary": "登录", "parameters": [{"description": "登录参数", "name": "request", "in": "body", "required": true, "schema": {"$ref": "#/definitions/daisy-server_api_v1.AuthLoginParams"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/daisy-server_api_v1.AuthLoginResponse"}}}}}, "/auth/logout": {"post": {"security": [{"Bearer": []}], "description": "注销登录", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["认证模块"], "summary": "注销", "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/daisy-server_api_v1.Response"}}}}}, "/auth/password": {"post": {"security": [{"Bearer": []}], "description": "修改当前登录用户的密码", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["认证模块"], "summary": "修改用户密码", "parameters": [{"description": "密码修改参数", "name": "request", "in": "body", "required": true, "schema": {"$ref": "#/definitions/daisy-server_api_v1.AuthPasswordUpdateParams"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/daisy-server_api_v1.Response"}}}}}, "/auth/refresh": {"post": {"description": "使用 refreshToken 刷新认证令牌", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["认证模块"], "summary": "刷新令牌", "parameters": [{"description": "刷新参数", "name": "request", "in": "body", "required": true, "schema": {"$ref": "#/definitions/daisy-server_api_v1.AuthRefreshParams"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/daisy-server_api_v1.AuthLoginResponse"}}}}}, "/auth/user_routes": {"get": {"security": [{"Bearer": []}], "description": "获取当前登录用户的菜单路由", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["认证模块"], "summary": "获取用户路由", "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/daisy-server_api_v1.Response"}}}}}, "/auth/userinfo": {"get": {"security": [{"Bearer": []}], "description": "获取当前登录用户的用户信息", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["认证模块"], "summary": "获取用户信息", "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/daisy-server_api_v1.AuthUserinfoResponse"}}}}, "post": {"security": [{"Bearer": []}], "description": "更新当前登录用户的用户信息", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["认证模块"], "summary": "更新用户信息", "parameters": [{"description": "更新参数", "name": "request", "in": "body", "required": true, "schema": {"$ref": "#/definitions/daisy-server_api_v1.AuthUserinfoUpdateParams"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/daisy-server_api_v1.Response"}}}}}, "/cms/metas": {"get": {"security": [{"Bearer": []}], "description": "分页获取栏目列表", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["内容模块", "栏目管理"], "summary": "获取栏目列表", "parameters": [{"type": "integer", "description": "当前页码(默认值: 1)", "name": "_page", "in": "query"}, {"type": "integer", "description": "每页数量(默认值: 10)", "name": "_limit", "in": "query"}, {"type": "string", "description": "排序字段，多个字段用逗号分隔", "name": "_sort", "in": "query"}, {"type": "string", "description": "排序方式，asc或desc，多个方式用逗号分隔", "name": "_order", "in": "query"}, {"type": "string", "description": "全文搜索", "name": "q", "in": "query"}, {"type": "boolean", "description": "状态筛选", "name": "status", "in": "query"}, {"type": "integer", "description": "父级ID筛选", "name": "parentId", "in": "query"}], "responses": {"200": {"description": "OK", "schema": {"allOf": [{"$ref": "#/definitions/daisy-server_api_v1.Response"}, {"type": "object", "properties": {"data": {"allOf": [{"$ref": "#/definitions/daisy-server_pkg_pagination.Result"}, {"type": "object", "properties": {"records": {"type": "array", "items": {"$ref": "#/definitions/daisy-server_api_v1.CmsMetaResponse"}}}}]}}}]}}}}, "post": {"security": [{"Bearer": []}], "description": "创建新的栏目记录", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["内容模块", "栏目管理"], "summary": "创建栏目", "parameters": [{"description": "栏目信息", "name": "request", "in": "body", "required": true, "schema": {"$ref": "#/definitions/daisy-server_api_v1.CmsMetaCreateParams"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/daisy-server_api_v1.Response"}}}}, "delete": {"security": [{"Bearer": []}], "description": "批量删除指定IDs的栏目", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["内容模块", "栏目管理"], "summary": "批量删除栏目", "parameters": [{"description": "栏目IDs", "name": "request", "in": "body", "required": true, "schema": {"$ref": "#/definitions/daisy-server_api_v1.BatchDeleteParams"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/daisy-server_api_v1.Response"}}}}}, "/cms/metas/{id}": {"get": {"security": [{"Bearer": []}], "description": "获取指定ID的栏目信息", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["内容模块", "栏目管理"], "summary": "获取栏目", "parameters": [{"type": "integer", "description": "栏目ID", "name": "id", "in": "path", "required": true}], "responses": {"200": {"description": "OK", "schema": {"allOf": [{"$ref": "#/definitions/daisy-server_api_v1.Response"}, {"type": "object", "properties": {"data": {"$ref": "#/definitions/daisy-server_api_v1.CmsMetaResponse"}}}]}}}}, "delete": {"security": [{"Bearer": []}], "description": "删除指定ID的栏目", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["内容模块", "栏目管理"], "summary": "删除栏目", "parameters": [{"type": "integer", "description": "栏目ID", "name": "id", "in": "path", "required": true}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/daisy-server_api_v1.Response"}}}}, "patch": {"security": [{"Bearer": []}], "description": "更新指定ID的栏目信息", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["内容模块", "栏目管理"], "summary": "更新栏目", "parameters": [{"type": "integer", "description": "栏目ID", "name": "id", "in": "path", "required": true}, {"description": "栏目信息", "name": "request", "in": "body", "required": true, "schema": {"$ref": "#/definitions/daisy-server_api_v1.CmsMetaUpdateParams"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/daisy-server_api_v1.Response"}}}}}, "/cms/posts": {"get": {"security": [{"Bearer": []}], "description": "分页获取文章列表", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["内容模块", "文章管理"], "summary": "获取文章列表", "parameters": [{"type": "integer", "description": "当前页码(默认值: 1)", "name": "_page", "in": "query"}, {"type": "integer", "description": "每页数量(默认值: 10)", "name": "_limit", "in": "query"}, {"type": "string", "description": "排序字段，多个字段用逗号分隔", "name": "_sort", "in": "query"}, {"type": "string", "description": "排序方式，asc或desc，多个方式用逗号分隔", "name": "_order", "in": "query"}, {"type": "string", "description": "全文搜索", "name": "q", "in": "query"}, {"type": "boolean", "description": "状态筛选", "name": "status", "in": "query"}, {"type": "integer", "description": "栏目ID筛选", "name": "metaId", "in": "query"}, {"type": "string", "description": "作者筛选", "name": "author", "in": "query"}, {"type": "string", "description": "标志筛选", "name": "flag", "in": "query"}], "responses": {"200": {"description": "OK", "schema": {"allOf": [{"$ref": "#/definitions/daisy-server_api_v1.Response"}, {"type": "object", "properties": {"data": {"allOf": [{"$ref": "#/definitions/daisy-server_pkg_pagination.Result"}, {"type": "object", "properties": {"records": {"type": "array", "items": {"$ref": "#/definitions/daisy-server_api_v1.CmsPostResponse"}}}}]}}}]}}}}, "post": {"security": [{"Bearer": []}], "description": "创建新的文章记录", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["内容模块", "文章管理"], "summary": "创建文章", "parameters": [{"description": "文章信息", "name": "request", "in": "body", "required": true, "schema": {"$ref": "#/definitions/daisy-server_api_v1.CmsPostCreateParams"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/daisy-server_api_v1.Response"}}}}, "delete": {"security": [{"Bearer": []}], "description": "批量删除指定IDs的文章", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["内容模块", "文章管理"], "summary": "批量删除文章", "parameters": [{"description": "文章IDs", "name": "request", "in": "body", "required": true, "schema": {"$ref": "#/definitions/daisy-server_api_v1.BatchDeleteParams"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/daisy-server_api_v1.Response"}}}}}, "/cms/posts/{id}": {"get": {"security": [{"Bearer": []}], "description": "获取指定ID的文章信息", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["内容模块", "文章管理"], "summary": "获取文章", "parameters": [{"type": "integer", "description": "文章ID", "name": "id", "in": "path", "required": true}], "responses": {"200": {"description": "OK", "schema": {"allOf": [{"$ref": "#/definitions/daisy-server_api_v1.Response"}, {"type": "object", "properties": {"data": {"$ref": "#/definitions/daisy-server_api_v1.CmsPostResponse"}}}]}}}}, "delete": {"security": [{"Bearer": []}], "description": "删除指定ID的文章", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["内容模块", "文章管理"], "summary": "删除文章", "parameters": [{"type": "integer", "description": "文章ID", "name": "id", "in": "path", "required": true}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/daisy-server_api_v1.Response"}}}}, "patch": {"security": [{"Bearer": []}], "description": "更新指定ID的文章信息", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["内容模块", "文章管理"], "summary": "更新文章", "parameters": [{"type": "integer", "description": "文章ID", "name": "id", "in": "path", "required": true}, {"description": "文章信息", "name": "request", "in": "body", "required": true, "schema": {"$ref": "#/definitions/daisy-server_api_v1.CmsPostUpdateParams"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/daisy-server_api_v1.Response"}}}}}, "/sys/tenants": {"get": {"security": [{"Bearer": []}], "description": "分页获取租户列表", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["系统模块", "租户管理"], "summary": "获取租户列表", "parameters": [{"type": "integer", "description": "当前页码(默认值: 1)", "name": "_page", "in": "query"}, {"type": "integer", "description": "每页数量(默认值: 10)", "name": "_limit", "in": "query"}, {"type": "string", "description": "排序字段，多个字段用逗号分隔", "name": "_sort", "in": "query"}, {"type": "string", "description": "排序方式，asc或desc，多个方式用逗号分隔", "name": "_order", "in": "query"}, {"type": "string", "description": "全文搜索", "name": "q", "in": "query"}, {"type": "boolean", "description": "状态筛选", "name": "status", "in": "query"}, {"type": "string", "description": "名称筛选", "name": "name", "in": "query"}, {"type": "string", "description": "编码筛选", "name": "code", "in": "query"}], "responses": {"200": {"description": "OK", "schema": {"allOf": [{"$ref": "#/definitions/daisy-server_api_v1.Response"}, {"type": "object", "properties": {"data": {"allOf": [{"$ref": "#/definitions/daisy-server_pkg_pagination.Result"}, {"type": "object", "properties": {"records": {"type": "array", "items": {"$ref": "#/definitions/daisy-server_api_v1.SysTenantResponse"}}}}]}}}]}}}}, "post": {"security": [{"Bearer": []}], "description": "创建新的租户记录", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["系统模块", "租户管理"], "summary": "创建租户", "parameters": [{"description": "租户信息", "name": "request", "in": "body", "required": true, "schema": {"$ref": "#/definitions/daisy-server_api_v1.SysTenantCreateParams"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/daisy-server_api_v1.Response"}}}}, "delete": {"security": [{"Bearer": []}], "description": "批量删除指定IDs的租户", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["系统模块", "租户管理"], "summary": "批量删除租户", "parameters": [{"description": "租户IDs", "name": "request", "in": "body", "required": true, "schema": {"$ref": "#/definitions/daisy-server_api_v1.BatchDeleteParams"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/daisy-server_api_v1.Response"}}}}}, "/sys/tenants/{id}": {"get": {"security": [{"Bearer": []}], "description": "获取指定ID的租户信息", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["系统模块", "租户管理"], "summary": "获取租户", "parameters": [{"type": "integer", "description": "租户ID", "name": "id", "in": "path", "required": true}], "responses": {"200": {"description": "OK", "schema": {"allOf": [{"$ref": "#/definitions/daisy-server_api_v1.Response"}, {"type": "object", "properties": {"data": {"$ref": "#/definitions/daisy-server_api_v1.SysTenantResponse"}}}]}}}}, "delete": {"security": [{"Bearer": []}], "description": "删除指定ID的租户", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["系统模块", "租户管理"], "summary": "删除租户", "parameters": [{"type": "integer", "description": "租户ID", "name": "id", "in": "path", "required": true}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/daisy-server_api_v1.Response"}}}}, "patch": {"security": [{"Bearer": []}], "description": "更新指定ID的租户信息", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["系统模块", "租户管理"], "summary": "更新租户", "parameters": [{"type": "integer", "description": "租户ID", "name": "id", "in": "path", "required": true}, {"description": "租户信息", "name": "request", "in": "body", "required": true, "schema": {"$ref": "#/definitions/daisy-server_api_v1.SysTenantUpdateParams"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/daisy-server_api_v1.Response"}}}}}, "/system/apis": {"get": {"security": [{"Bearer": []}], "description": "分页获取接口列表", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["系统模块", "接口管理"], "summary": "获取接口列表", "parameters": [{"type": "integer", "description": "当前页码(默认值: 1)", "name": "_page", "in": "query"}, {"type": "integer", "description": "每页数量(默认值: 10)", "name": "_limit", "in": "query"}, {"type": "string", "description": "排序字段，多个字段用逗号分隔", "name": "_sort", "in": "query"}, {"type": "string", "description": "排序方式，asc或desc，多个方式用逗号分隔", "name": "_order", "in": "query"}, {"type": "string", "description": "全文搜索", "name": "q", "in": "query"}, {"type": "boolean", "description": "状态筛选", "name": "status", "in": "query"}], "responses": {"200": {"description": "OK", "schema": {"allOf": [{"$ref": "#/definitions/daisy-server_api_v1.Response"}, {"type": "object", "properties": {"data": {"allOf": [{"$ref": "#/definitions/daisy-server_pkg_pagination.Result"}, {"type": "object", "properties": {"records": {"type": "array", "items": {"$ref": "#/definitions/daisy-server_api_v1.SysApiResponse"}}}}]}}}]}}}}, "post": {"security": [{"Bearer": []}], "description": "创建新的接口记录", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["系统模块", "接口管理"], "summary": "创建接口", "parameters": [{"description": "接口信息", "name": "request", "in": "body", "required": true, "schema": {"$ref": "#/definitions/daisy-server_api_v1.SysApiCreateParams"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/daisy-server_api_v1.Response"}}}}, "delete": {"security": [{"Bearer": []}], "description": "批量删除指定IDs的接口", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["系统模块", "接口管理"], "summary": "批量删除接口", "parameters": [{"description": "接口IDs", "name": "request", "in": "body", "required": true, "schema": {"$ref": "#/definitions/daisy-server_api_v1.BatchDeleteParams"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/daisy-server_api_v1.Response"}}}}}, "/system/apis/refresh": {"get": {"security": [{"Bearer": []}], "description": "重置所有接口信息", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["系统模块", "接口管理"], "summary": "刷新接口表", "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/daisy-server_api_v1.Response"}}}}}, "/system/apis/{id}": {"get": {"security": [{"Bearer": []}], "description": "获取指定ID的接口信息", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["系统模块", "接口管理"], "summary": "获取接口", "parameters": [{"type": "integer", "description": "接口ID", "name": "id", "in": "path", "required": true}], "responses": {"200": {"description": "OK", "schema": {"allOf": [{"$ref": "#/definitions/daisy-server_api_v1.Response"}, {"type": "object", "properties": {"data": {"$ref": "#/definitions/daisy-server_api_v1.SysApiResponse"}}}]}}}}, "delete": {"security": [{"Bearer": []}], "description": "删除指定ID的接口", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["系统模块", "接口管理"], "summary": "删除接口", "parameters": [{"type": "integer", "description": "接口ID", "name": "id", "in": "path", "required": true}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/daisy-server_api_v1.Response"}}}}, "patch": {"security": [{"Bearer": []}], "description": "更新指定ID的接口信息", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["系统模块", "接口管理"], "summary": "更新接口", "parameters": [{"type": "integer", "description": "接口ID", "name": "id", "in": "path", "required": true}, {"description": "接口信息", "name": "request", "in": "body", "required": true, "schema": {"$ref": "#/definitions/daisy-server_api_v1.SysApiUpdateParams"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/daisy-server_api_v1.Response"}}}}}, "/system/configs": {"get": {"security": [{"Bearer": []}], "description": "分页获取配置列表", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["系统模块", "配置管理"], "summary": "获取配置列表", "parameters": [{"type": "integer", "description": "当前页码(默认值: 1)", "name": "_page", "in": "query"}, {"type": "integer", "description": "每页数量(默认值: 10)", "name": "_limit", "in": "query"}, {"type": "string", "description": "排序字段，多个字段用逗号分隔", "name": "_sort", "in": "query"}, {"type": "string", "description": "排序方式，asc或desc，多个方式用逗号分隔", "name": "_order", "in": "query"}, {"type": "string", "description": "全文搜索", "name": "q", "in": "query"}, {"type": "boolean", "description": "状态筛选", "name": "status", "in": "query"}], "responses": {"200": {"description": "OK", "schema": {"allOf": [{"$ref": "#/definitions/daisy-server_api_v1.Response"}, {"type": "object", "properties": {"data": {"allOf": [{"$ref": "#/definitions/daisy-server_pkg_pagination.Result"}, {"type": "object", "properties": {"records": {"type": "array", "items": {"$ref": "#/definitions/daisy-server_api_v1.SysConfigResponse"}}}}]}}}]}}}}, "post": {"security": [{"Bearer": []}], "description": "创建新的配置记录", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["系统模块", "配置管理"], "summary": "创建配置", "parameters": [{"description": "配置信息", "name": "request", "in": "body", "required": true, "schema": {"$ref": "#/definitions/daisy-server_api_v1.SysConfigCreateParams"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/daisy-server_api_v1.Response"}}}}, "delete": {"security": [{"Bearer": []}], "description": "批量删除指定IDs的配置", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["系统模块", "配置管理"], "summary": "批量删除配置", "parameters": [{"description": "配置IDs", "name": "request", "in": "body", "required": true, "schema": {"$ref": "#/definitions/daisy-server_api_v1.BatchDeleteParams"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/daisy-server_api_v1.Response"}}}}}, "/system/configs/{id}": {"get": {"security": [{"Bearer": []}], "description": "获取指定ID的配置信息", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["系统模块", "配置管理"], "summary": "获取配置", "parameters": [{"type": "integer", "description": "配置ID", "name": "id", "in": "path", "required": true}], "responses": {"200": {"description": "OK", "schema": {"allOf": [{"$ref": "#/definitions/daisy-server_api_v1.Response"}, {"type": "object", "properties": {"data": {"$ref": "#/definitions/daisy-server_api_v1.SysConfigResponse"}}}]}}}}, "delete": {"security": [{"Bearer": []}], "description": "删除指定ID的配置", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["系统模块", "配置管理"], "summary": "删除配置", "parameters": [{"type": "integer", "description": "配置ID", "name": "id", "in": "path", "required": true}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/daisy-server_api_v1.Response"}}}}, "patch": {"security": [{"Bearer": []}], "description": "更新指定ID的配置信息", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["系统模块", "配置管理"], "summary": "更新配置", "parameters": [{"type": "integer", "description": "配置ID", "name": "id", "in": "path", "required": true}, {"description": "配置信息", "name": "request", "in": "body", "required": true, "schema": {"$ref": "#/definitions/daisy-server_api_v1.SysConfigUpdateParams"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/daisy-server_api_v1.Response"}}}}}, "/system/dicts": {"get": {"security": [{"Bearer": []}], "description": "分页获取字典列表", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["系统模块", "字典管理"], "summary": "获取字典列表", "parameters": [{"type": "integer", "description": "当前页码(默认值: 1)", "name": "_page", "in": "query"}, {"type": "integer", "description": "每页数量(默认值: 10)", "name": "_limit", "in": "query"}, {"type": "string", "description": "排序字段，多个字段用逗号分隔", "name": "_sort", "in": "query"}, {"type": "string", "description": "排序方式，asc或desc，多个方式用逗号分隔", "name": "_order", "in": "query"}, {"type": "string", "description": "全文搜索", "name": "q", "in": "query"}, {"type": "boolean", "description": "状态筛选", "name": "status", "in": "query"}], "responses": {"200": {"description": "OK", "schema": {"allOf": [{"$ref": "#/definitions/daisy-server_api_v1.Response"}, {"type": "object", "properties": {"data": {"allOf": [{"$ref": "#/definitions/daisy-server_pkg_pagination.Result"}, {"type": "object", "properties": {"records": {"type": "array", "items": {"$ref": "#/definitions/daisy-server_api_v1.SysDictResponse"}}}}]}}}]}}}}, "post": {"security": [{"Bearer": []}], "description": "创建新的字典记录", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["系统模块", "字典管理"], "summary": "创建字典", "parameters": [{"description": "字典信息", "name": "request", "in": "body", "required": true, "schema": {"$ref": "#/definitions/daisy-server_api_v1.SysDictCreateParams"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/daisy-server_api_v1.Response"}}}}, "delete": {"security": [{"Bearer": []}], "description": "批量删除指定IDs的字典", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["系统模块", "字典管理"], "summary": "批量删除字典", "parameters": [{"description": "字典IDs", "name": "request", "in": "body", "required": true, "schema": {"$ref": "#/definitions/daisy-server_api_v1.BatchDeleteParams"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/daisy-server_api_v1.Response"}}}}}, "/system/dicts/{id}": {"get": {"security": [{"Bearer": []}], "description": "获取指定ID的字典信息", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["系统模块", "字典管理"], "summary": "获取字典", "parameters": [{"type": "integer", "description": "字典ID", "name": "id", "in": "path", "required": true}], "responses": {"200": {"description": "OK", "schema": {"allOf": [{"$ref": "#/definitions/daisy-server_api_v1.Response"}, {"type": "object", "properties": {"data": {"$ref": "#/definitions/daisy-server_api_v1.SysDictResponse"}}}]}}}}, "delete": {"security": [{"Bearer": []}], "description": "删除指定ID的字典", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["系统模块", "字典管理"], "summary": "删除字典", "parameters": [{"type": "integer", "description": "字典ID", "name": "id", "in": "path", "required": true}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/daisy-server_api_v1.Response"}}}}, "patch": {"security": [{"Bearer": []}], "description": "更新指定ID的字典信息", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["系统模块", "字典管理"], "summary": "更新字典", "parameters": [{"type": "integer", "description": "字典ID", "name": "id", "in": "path", "required": true}, {"description": "字典信息", "name": "request", "in": "body", "required": true, "schema": {"$ref": "#/definitions/daisy-server_api_v1.SysDictUpdateParams"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/daisy-server_api_v1.Response"}}}}}, "/system/logs": {"get": {"security": [{"Bearer": []}], "description": "分页获取日志列表", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["系统模块", "日志管理"], "summary": "获取日志列表", "parameters": [{"type": "integer", "description": "当前页码(默认值: 1)", "name": "_page", "in": "query"}, {"type": "integer", "description": "每页数量(默认值: 10)", "name": "_limit", "in": "query"}, {"type": "string", "description": "排序字段，多个字段用逗号分隔", "name": "_sort", "in": "query"}, {"type": "string", "description": "排序方式，asc或desc，多个方式用逗号分隔", "name": "_order", "in": "query"}, {"type": "string", "description": "全文搜索", "name": "q", "in": "query"}, {"type": "string", "description": "请求方法筛选", "name": "method", "in": "query"}, {"type": "integer", "description": "状态码筛选", "name": "code", "in": "query"}], "responses": {"200": {"description": "OK", "schema": {"allOf": [{"$ref": "#/definitions/daisy-server_api_v1.Response"}, {"type": "object", "properties": {"data": {"allOf": [{"$ref": "#/definitions/daisy-server_pkg_pagination.Result"}, {"type": "object", "properties": {"records": {"type": "array", "items": {"$ref": "#/definitions/daisy-server_api_v1.SysLogResponse"}}}}]}}}]}}}}, "delete": {"security": [{"Bearer": []}], "description": "批量删除指定IDs的日志", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["系统模块", "日志管理"], "summary": "批量删除日志", "parameters": [{"description": "日志IDs", "name": "request", "in": "body", "required": true, "schema": {"$ref": "#/definitions/daisy-server_api_v1.BatchDeleteParams"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/daisy-server_api_v1.Response"}}}}}, "/system/logs/{id}": {"get": {"security": [{"Bearer": []}], "description": "获取指定ID的日志信息", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["系统模块", "日志管理"], "summary": "获取日志", "parameters": [{"type": "integer", "description": "日志ID", "name": "id", "in": "path", "required": true}], "responses": {"200": {"description": "OK", "schema": {"allOf": [{"$ref": "#/definitions/daisy-server_api_v1.Response"}, {"type": "object", "properties": {"data": {"$ref": "#/definitions/daisy-server_api_v1.SysLogResponse"}}}]}}}}, "delete": {"security": [{"Bearer": []}], "description": "删除指定ID的日志", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["系统模块", "日志管理"], "summary": "删除日志", "parameters": [{"type": "integer", "description": "日志ID", "name": "id", "in": "path", "required": true}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/daisy-server_api_v1.Response"}}}}}, "/system/menus": {"get": {"security": [{"Bearer": []}], "description": "分页获取菜单列表", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["系统模块", "菜单管理"], "summary": "获取菜单列表", "parameters": [{"type": "integer", "description": "当前页码(默认值: 1)", "name": "_page", "in": "query"}, {"type": "integer", "description": "每页数量(默认值: 10)", "name": "_limit", "in": "query"}, {"type": "string", "description": "排序字段，多个字段用逗号分隔", "name": "_sort", "in": "query"}, {"type": "string", "description": "排序方式，asc或desc，多个方式用逗号分隔", "name": "_order", "in": "query"}, {"type": "string", "description": "全文搜索", "name": "q", "in": "query"}, {"type": "boolean", "description": "状态筛选", "name": "status", "in": "query"}, {"type": "boolean", "description": "常量筛选", "name": "constant", "in": "query"}], "responses": {"200": {"description": "OK", "schema": {"allOf": [{"$ref": "#/definitions/daisy-server_api_v1.Response"}, {"type": "object", "properties": {"data": {"allOf": [{"$ref": "#/definitions/daisy-server_pkg_pagination.Result"}, {"type": "object", "properties": {"records": {"type": "array", "items": {"$ref": "#/definitions/daisy-server_api_v1.SysMenuResponse"}}}}]}}}]}}}}, "post": {"security": [{"Bearer": []}], "description": "创建新的菜单", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["系统模块", "菜单管理"], "summary": "创建菜单", "parameters": [{"description": "菜单信息", "name": "request", "in": "body", "required": true, "schema": {"$ref": "#/definitions/daisy-server_api_v1.SysMenuCreateParams"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/daisy-server_api_v1.Response"}}}}, "delete": {"security": [{"Bearer": []}], "description": "批量删除指定IDs的菜单", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["系统模块", "菜单管理"], "summary": "批量删除菜单", "parameters": [{"description": "菜单IDs", "name": "request", "in": "body", "required": true, "schema": {"$ref": "#/definitions/daisy-server_api_v1.BatchDeleteParams"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/daisy-server_api_v1.Response"}}}}}, "/system/menus/{id}": {"get": {"security": [{"Bearer": []}], "description": "获取指定ID的菜单信息", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["系统模块", "菜单管理"], "summary": "获取菜单", "parameters": [{"type": "integer", "description": "菜单ID", "name": "id", "in": "path", "required": true}], "responses": {"200": {"description": "OK", "schema": {"allOf": [{"$ref": "#/definitions/daisy-server_api_v1.Response"}, {"type": "object", "properties": {"data": {"$ref": "#/definitions/daisy-server_api_v1.SysMenuResponse"}}}]}}}}, "delete": {"security": [{"Bearer": []}], "description": "删除指定ID的菜单", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["系统模块", "菜单管理"], "summary": "删除菜单", "parameters": [{"type": "integer", "description": "菜单ID", "name": "id", "in": "path", "required": true}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/daisy-server_api_v1.Response"}}}}, "patch": {"security": [{"Bearer": []}], "description": "更新指定ID的菜单信息", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["系统模块", "菜单管理"], "summary": "更新菜单", "parameters": [{"type": "integer", "description": "菜单ID", "name": "id", "in": "path", "required": true}, {"description": "菜单信息", "name": "request", "in": "body", "required": true, "schema": {"$ref": "#/definitions/daisy-server_api_v1.SysMenuUpdateParams"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/daisy-server_api_v1.Response"}}}}}, "/system/roles": {"get": {"security": [{"Bearer": []}], "description": "分页获取角色列表", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["系统模块", "角色管理"], "summary": "获取角色列表", "parameters": [{"type": "integer", "description": "当前页码(默认值: 1)", "name": "_page", "in": "query"}, {"type": "integer", "description": "每页数量(默认值: 10)", "name": "_limit", "in": "query"}, {"type": "string", "description": "排序字段，多个字段用逗号分隔", "name": "_sort", "in": "query"}, {"type": "string", "description": "排序方式，asc或desc，多个方式用逗号分隔", "name": "_order", "in": "query"}, {"type": "string", "description": "全文搜索", "name": "q", "in": "query"}, {"type": "boolean", "description": "状态筛选", "name": "status", "in": "query"}], "responses": {"200": {"description": "OK", "schema": {"allOf": [{"$ref": "#/definitions/daisy-server_api_v1.Response"}, {"type": "object", "properties": {"data": {"allOf": [{"$ref": "#/definitions/daisy-server_pkg_pagination.Result"}, {"type": "object", "properties": {"records": {"type": "array", "items": {"$ref": "#/definitions/daisy-server_api_v1.SysRoleResponse"}}}}]}}}]}}}}, "post": {"security": [{"Bearer": []}], "description": "创建新的角色", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["系统模块", "角色管理"], "summary": "创建角色", "parameters": [{"description": "角色信息", "name": "request", "in": "body", "required": true, "schema": {"$ref": "#/definitions/daisy-server_api_v1.SysRoleCreateParams"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/daisy-server_api_v1.Response"}}}}, "delete": {"security": [{"Bearer": []}], "description": "批量删除指定IDs的角色", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["系统模块", "角色管理"], "summary": "批量删除角色", "parameters": [{"description": "角色IDs", "name": "request", "in": "body", "required": true, "schema": {"$ref": "#/definitions/daisy-server_api_v1.BatchDeleteParams"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/daisy-server_api_v1.Response"}}}}}, "/system/roles/{id}": {"get": {"security": [{"Bearer": []}], "description": "获取指定ID的角色信息", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["系统模块", "角色管理"], "summary": "获取角色", "parameters": [{"type": "integer", "description": "角色ID", "name": "id", "in": "path", "required": true}], "responses": {"200": {"description": "OK", "schema": {"allOf": [{"$ref": "#/definitions/daisy-server_api_v1.Response"}, {"type": "object", "properties": {"data": {"$ref": "#/definitions/daisy-server_api_v1.SysRoleResponse"}}}]}}}}, "delete": {"security": [{"Bearer": []}], "description": "删除指定ID的角色", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["系统模块", "角色管理"], "summary": "删除角色", "parameters": [{"type": "integer", "description": "角色ID", "name": "id", "in": "path", "required": true}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/daisy-server_api_v1.Response"}}}}, "patch": {"security": [{"Bearer": []}], "description": "更新指定ID的角色信息", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["系统模块", "角色管理"], "summary": "更新角色", "parameters": [{"type": "integer", "description": "角色ID", "name": "id", "in": "path", "required": true}, {"description": "角色信息", "name": "request", "in": "body", "required": true, "schema": {"$ref": "#/definitions/daisy-server_api_v1.SysRoleUpdateParams"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/daisy-server_api_v1.Response"}}}}}, "/system/roles/{id}/permit": {"patch": {"security": [{"Bearer": []}], "description": "为指定角色分配菜单和API权限", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["系统模块", "角色管理"], "summary": "角色权限分配", "parameters": [{"type": "integer", "description": "角色ID", "name": "id", "in": "path", "required": true}, {"description": "权限分配参数", "name": "request", "in": "body", "required": true, "schema": {"$ref": "#/definitions/daisy-server_api_v1.SysRolePermitParams"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/daisy-server_api_v1.Response"}}}}}, "/system/users": {"get": {"security": [{"Bearer": []}], "description": "分页获取用户列表", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["系统模块", "用户管理"], "summary": "获取用户列表", "parameters": [{"type": "integer", "description": "当前页码(默认值: 1)", "name": "_page", "in": "query"}, {"type": "integer", "description": "每页数量(默认值: 10)", "name": "_limit", "in": "query"}, {"type": "string", "description": "排序字段，多个字段用逗号分隔", "name": "_sort", "in": "query"}, {"type": "string", "description": "排序方式，asc或desc，多个方式用逗号分隔", "name": "_order", "in": "query"}, {"type": "string", "description": "全文搜索", "name": "q", "in": "query"}, {"type": "boolean", "description": "状态筛选", "name": "status", "in": "query"}, {"type": "string", "description": "用户名筛选", "name": "username", "in": "query"}, {"type": "string", "description": "昵称筛选", "name": "nickname", "in": "query"}, {"type": "string", "description": "手机号筛选", "name": "phone", "in": "query"}, {"type": "string", "description": "邮箱筛选", "name": "email", "in": "query"}, {"type": "integer", "description": "租户ID筛选", "name": "tenantId", "in": "query"}, {"type": "string", "description": "展开关联信息，支持: tenant", "name": "_expand", "in": "query"}], "responses": {"200": {"description": "OK", "schema": {"allOf": [{"$ref": "#/definitions/daisy-server_api_v1.Response"}, {"type": "object", "properties": {"data": {"allOf": [{"$ref": "#/definitions/daisy-server_pkg_pagination.Result"}, {"type": "object", "properties": {"records": {"type": "array", "items": {"$ref": "#/definitions/daisy-server_api_v1.SysUserResponse"}}}}]}}}]}}}}, "post": {"security": [{"Bearer": []}], "description": "创建新的用户", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["系统模块", "用户管理"], "summary": "创建用户", "parameters": [{"description": "用户信息", "name": "request", "in": "body", "required": true, "schema": {"$ref": "#/definitions/daisy-server_api_v1.SysUserCreateParams"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/daisy-server_api_v1.Response"}}}}, "delete": {"security": [{"Bearer": []}], "description": "批量删除指定IDs的用户", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["系统模块", "用户管理"], "summary": "批量删除用户", "parameters": [{"description": "用户IDs", "name": "request", "in": "body", "required": true, "schema": {"$ref": "#/definitions/daisy-server_api_v1.BatchDeleteParams"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/daisy-server_api_v1.Response"}}}}}, "/system/users/{id}": {"get": {"security": [{"Bearer": []}], "description": "获取指定ID的用户信息", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["系统模块", "用户管理"], "summary": "获取用户", "parameters": [{"type": "integer", "description": "用户ID", "name": "id", "in": "path", "required": true}, {"type": "string", "description": "展开关联信息，支持: tenant", "name": "_expand", "in": "query"}], "responses": {"200": {"description": "OK", "schema": {"allOf": [{"$ref": "#/definitions/daisy-server_api_v1.Response"}, {"type": "object", "properties": {"data": {"$ref": "#/definitions/daisy-server_api_v1.SysUserResponse"}}}]}}}}, "delete": {"security": [{"Bearer": []}], "description": "删除指定ID的用户", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["系统模块", "用户管理"], "summary": "删除用户", "parameters": [{"type": "integer", "description": "用户ID", "name": "id", "in": "path", "required": true}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/daisy-server_api_v1.Response"}}}}, "patch": {"security": [{"Bearer": []}], "description": "更新指定ID的用户信息", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["系统模块", "用户管理"], "summary": "更新用户", "parameters": [{"type": "integer", "description": "用户ID", "name": "id", "in": "path", "required": true}, {"description": "用户信息", "name": "request", "in": "body", "required": true, "schema": {"$ref": "#/definitions/daisy-server_api_v1.SysUserUpdateParams"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/daisy-server_api_v1.Response"}}}}}, "/upload/file": {"post": {"security": [{"Bearer": []}], "description": "上传文件到MinIO服务器", "consumes": ["multipart/form-data"], "produces": ["application/json"], "tags": ["上传模块"], "summary": "上传文件", "parameters": [{"type": "file", "description": "文件", "name": "file", "in": "formData", "required": true}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/daisy-server_api_v1.Response"}}}}}, "/upload/image": {"post": {"security": [{"Bearer": []}], "description": "上传图片到MinIO服务器", "consumes": ["multipart/form-data"], "produces": ["application/json"], "tags": ["上传模块"], "summary": "上传图片", "parameters": [{"type": "file", "description": "图片文件", "name": "file", "in": "formData", "required": true}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/daisy-server_api_v1.Response"}}}}}, "/upload/remove": {"delete": {"security": [{"Bearer": []}], "description": "从MinIO服务器删除资源", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["上传模块"], "summary": "删除资源", "parameters": [{"description": "文件信息", "name": "request", "in": "body", "required": true, "schema": {"$ref": "#/definitions/daisy-server_api_v1.UploadRemoveParams"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/daisy-server_api_v1.Response"}}}}}, "/wms/areas": {"get": {"security": [{"Bearer": []}], "description": "分页获取仓库区域列表", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["仓储模块", "仓库管理"], "summary": "获取仓库区域列表", "parameters": [{"type": "integer", "description": "当前页码(默认值: 1)", "name": "_page", "in": "query"}, {"type": "integer", "description": "每页数量(默认值: 10)", "name": "_limit", "in": "query"}, {"type": "string", "description": "排序字段，多个字段用逗号分隔", "name": "_sort", "in": "query"}, {"type": "string", "description": "排序方式，asc或desc，多个方式用逗号分隔", "name": "_order", "in": "query"}, {"type": "string", "description": "全文搜索", "name": "q", "in": "query"}, {"type": "boolean", "description": "状态筛选", "name": "status", "in": "query"}, {"type": "integer", "description": "类型筛选", "name": "type", "in": "query"}], "responses": {"200": {"description": "OK", "schema": {"allOf": [{"$ref": "#/definitions/daisy-server_api_v1.Response"}, {"type": "object", "properties": {"data": {"allOf": [{"$ref": "#/definitions/daisy-server_pkg_pagination.Result"}, {"type": "object", "properties": {"records": {"type": "array", "items": {"$ref": "#/definitions/daisy-server_api_v1.WmsAreaResponse"}}}}]}}}]}}}}, "post": {"security": [{"Bearer": []}], "description": "创建新的仓库区域记录", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["仓储模块", "仓库管理"], "summary": "创建仓库区域", "parameters": [{"description": "仓库区域信息", "name": "request", "in": "body", "required": true, "schema": {"$ref": "#/definitions/daisy-server_api_v1.WmsAreaCreateParams"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/daisy-server_api_v1.Response"}}}}, "delete": {"security": [{"Bearer": []}], "description": "批量删除指定IDs的仓库区域", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["仓储模块", "仓库管理"], "summary": "批量删除仓库区域", "parameters": [{"description": "仓库区域IDs", "name": "request", "in": "body", "required": true, "schema": {"$ref": "#/definitions/daisy-server_api_v1.BatchDeleteParams"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/daisy-server_api_v1.Response"}}}}}, "/wms/areas/{id}": {"get": {"security": [{"Bearer": []}], "description": "获取指定ID的仓库区域信息", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["仓储模块", "仓库管理"], "summary": "获取仓库区域", "parameters": [{"type": "integer", "description": "仓库区域ID", "name": "id", "in": "path", "required": true}], "responses": {"200": {"description": "OK", "schema": {"allOf": [{"$ref": "#/definitions/daisy-server_api_v1.Response"}, {"type": "object", "properties": {"data": {"$ref": "#/definitions/daisy-server_api_v1.WmsAreaResponse"}}}]}}}}, "delete": {"security": [{"Bearer": []}], "description": "删除指定ID的仓库区域", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["仓储模块", "仓库管理"], "summary": "删除仓库区域", "parameters": [{"type": "integer", "description": "仓库区域ID", "name": "id", "in": "path", "required": true}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/daisy-server_api_v1.Response"}}}}, "patch": {"security": [{"Bearer": []}], "description": "更新指定ID的仓库区域信息", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["仓储模块", "仓库管理"], "summary": "更新仓库区域", "parameters": [{"type": "integer", "description": "仓库区域ID", "name": "id", "in": "path", "required": true}, {"description": "仓库区域信息", "name": "request", "in": "body", "required": true, "schema": {"$ref": "#/definitions/daisy-server_api_v1.WmsAreaUpdateParams"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/daisy-server_api_v1.Response"}}}}}, "/wms/check-skus": {"get": {"security": [{"Bearer": []}], "description": "分页获取盘点单物料规格列表", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["仓储模块", "盘点单物料规格管理"], "summary": "获取盘点单物料规格列表", "parameters": [{"type": "integer", "description": "当前页码(默认值: 1)", "name": "_page", "in": "query"}, {"type": "integer", "description": "每页数量(默认值: 10)", "name": "_limit", "in": "query"}, {"type": "string", "description": "排序字段，多个字段用逗号分隔", "name": "_sort", "in": "query"}, {"type": "string", "description": "排序方式，asc或desc，多个方式用逗号分隔", "name": "_order", "in": "query"}, {"type": "string", "description": "全文搜索", "name": "q", "in": "query"}, {"type": "boolean", "description": "状态筛选", "name": "status", "in": "query"}, {"type": "integer", "description": "盘点单ID筛选", "name": "checkId", "in": "query"}, {"type": "integer", "description": "物料ID筛选", "name": "itemId", "in": "query"}, {"type": "integer", "description": "规格ID筛选", "name": "skuId", "in": "query"}, {"type": "string", "description": "展开关联信息，支持: check,item,sku", "name": "_expand", "in": "query"}], "responses": {"200": {"description": "OK", "schema": {"allOf": [{"$ref": "#/definitions/daisy-server_api_v1.Response"}, {"type": "object", "properties": {"data": {"allOf": [{"$ref": "#/definitions/daisy-server_pkg_pagination.Result"}, {"type": "object", "properties": {"records": {"type": "array", "items": {"$ref": "#/definitions/daisy-server_api_v1.WmsCheckSkuResponse"}}}}]}}}]}}}}, "post": {"security": [{"Bearer": []}], "description": "创建新的盘点单物料规格记录", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["仓储模块", "盘点单物料规格管理"], "summary": "创建盘点单物料规格", "parameters": [{"description": "盘点单物料规格信息", "name": "request", "in": "body", "required": true, "schema": {"$ref": "#/definitions/daisy-server_api_v1.WmsCheckSkuCreateParams"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/daisy-server_api_v1.Response"}}}}, "delete": {"security": [{"Bearer": []}], "description": "批量删除指定IDs的盘点单物料规格", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["仓储模块", "盘点单物料规格管理"], "summary": "批量删除盘点单物料规格", "parameters": [{"description": "盘点单物料规格IDs", "name": "request", "in": "body", "required": true, "schema": {"$ref": "#/definitions/daisy-server_api_v1.BatchDeleteParams"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/daisy-server_api_v1.Response"}}}}}, "/wms/check-skus/{id}": {"get": {"security": [{"Bearer": []}], "description": "获取指定ID的盘点单物料规格信息", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["仓储模块", "盘点单物料规格管理"], "summary": "获取盘点单物料规格", "parameters": [{"type": "integer", "description": "盘点单物料规格ID", "name": "id", "in": "path", "required": true}, {"type": "string", "description": "展开关联信息，支持: check,item,sku", "name": "_expand", "in": "query"}], "responses": {"200": {"description": "OK", "schema": {"allOf": [{"$ref": "#/definitions/daisy-server_api_v1.Response"}, {"type": "object", "properties": {"data": {"$ref": "#/definitions/daisy-server_api_v1.WmsCheckSkuResponse"}}}]}}}}, "delete": {"security": [{"Bearer": []}], "description": "删除指定ID的盘点单物料规格", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["仓储模块", "盘点单物料规格管理"], "summary": "删除盘点单物料规格", "parameters": [{"type": "integer", "description": "盘点单物料规格ID", "name": "id", "in": "path", "required": true}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/daisy-server_api_v1.Response"}}}}, "patch": {"security": [{"Bearer": []}], "description": "更新指定ID的盘点单物料规格信息", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["仓储模块", "盘点单物料规格管理"], "summary": "更新盘点单物料规格", "parameters": [{"type": "integer", "description": "盘点单物料规格ID", "name": "id", "in": "path", "required": true}, {"description": "盘点单物料规格信息", "name": "request", "in": "body", "required": true, "schema": {"$ref": "#/definitions/daisy-server_api_v1.WmsCheckSkuUpdateParams"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/daisy-server_api_v1.Response"}}}}}, "/wms/checks": {"get": {"security": [{"Bearer": []}], "description": "分页获取盘点单列表", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["仓储模块", "盘点单管理"], "summary": "获取盘点单列表", "parameters": [{"type": "integer", "description": "当前页码(默认值: 1)", "name": "_page", "in": "query"}, {"type": "integer", "description": "每页数量(默认值: 10)", "name": "_limit", "in": "query"}, {"type": "string", "description": "排序字段，多个字段用逗号分隔", "name": "_sort", "in": "query"}, {"type": "string", "description": "排序方式，asc或desc，多个方式用逗号分隔", "name": "_order", "in": "query"}, {"type": "string", "description": "全文搜索", "name": "q", "in": "query"}, {"type": "integer", "description": "状态筛选 1草稿 2待审核 3作业中 4已完成 5已作废", "name": "status", "in": "query"}, {"type": "string", "description": "编号筛选", "name": "code", "in": "query"}, {"type": "integer", "description": "类型筛选", "name": "type", "in": "query"}, {"type": "string", "description": "库位ID筛选", "name": "areaId", "in": "query"}], "responses": {"200": {"description": "OK", "schema": {"allOf": [{"$ref": "#/definitions/daisy-server_api_v1.Response"}, {"type": "object", "properties": {"data": {"allOf": [{"$ref": "#/definitions/daisy-server_pkg_pagination.Result"}, {"type": "object", "properties": {"records": {"type": "array", "items": {"$ref": "#/definitions/daisy-server_api_v1.WmsCheckResponse"}}}}]}}}]}}}}, "post": {"security": [{"Bearer": []}], "description": "创建新的盘点单记录", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["仓储模块", "盘点单管理"], "summary": "创建盘点单", "parameters": [{"description": "盘点单信息", "name": "request", "in": "body", "required": true, "schema": {"$ref": "#/definitions/daisy-server_api_v1.WmsCheckCreateParams"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/daisy-server_api_v1.Response"}}}}, "delete": {"security": [{"Bearer": []}], "description": "批量删除指定IDs的盘点单", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["仓储模块", "盘点单管理"], "summary": "批量删除盘点单", "parameters": [{"description": "盘点单IDs", "name": "request", "in": "body", "required": true, "schema": {"$ref": "#/definitions/daisy-server_api_v1.BatchDeleteParams"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/daisy-server_api_v1.Response"}}}}}, "/wms/checks/{id}": {"get": {"security": [{"Bearer": []}], "description": "获取指定ID的盘点单信息", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["仓储模块", "盘点单管理"], "summary": "获取盘点单", "parameters": [{"type": "integer", "description": "盘点单ID", "name": "id", "in": "path", "required": true}], "responses": {"200": {"description": "OK", "schema": {"allOf": [{"$ref": "#/definitions/daisy-server_api_v1.Response"}, {"type": "object", "properties": {"data": {"$ref": "#/definitions/daisy-server_api_v1.WmsCheckResponse"}}}]}}}}, "delete": {"security": [{"Bearer": []}], "description": "删除指定ID的盘点单", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["仓储模块", "盘点单管理"], "summary": "删除盘点单", "parameters": [{"type": "integer", "description": "盘点单ID", "name": "id", "in": "path", "required": true}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/daisy-server_api_v1.Response"}}}}, "patch": {"security": [{"Bearer": []}], "description": "更新指定ID的盘点单信息", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["仓储模块", "盘点单管理"], "summary": "更新盘点单", "parameters": [{"type": "integer", "description": "盘点单ID", "name": "id", "in": "path", "required": true}, {"description": "盘点单信息", "name": "request", "in": "body", "required": true, "schema": {"$ref": "#/definitions/daisy-server_api_v1.WmsCheckUpdateParams"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/daisy-server_api_v1.Response"}}}}}, "/wms/deliver-skus": {"get": {"security": [{"Bearer": []}], "description": "分页获取出库单物料规格列表", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["仓储模块", "出库单物料规格管理"], "summary": "获取出库单物料规格列表", "parameters": [{"type": "integer", "description": "当前页码(默认值: 1)", "name": "_page", "in": "query"}, {"type": "integer", "description": "每页数量(默认值: 10)", "name": "_limit", "in": "query"}, {"type": "string", "description": "排序字段，多个字段用逗号分隔", "name": "_sort", "in": "query"}, {"type": "string", "description": "排序方式，asc或desc，多个方式用逗号分隔", "name": "_order", "in": "query"}, {"type": "string", "description": "全文搜索", "name": "q", "in": "query"}, {"type": "boolean", "description": "状态筛选", "name": "status", "in": "query"}, {"type": "integer", "description": "出库单ID筛选", "name": "deliverId", "in": "query"}, {"type": "integer", "description": "物料ID筛选", "name": "itemId", "in": "query"}, {"type": "integer", "description": "规格ID筛选", "name": "skuId", "in": "query"}, {"type": "string", "description": "展开关联信息，支持: deliver,item,sku", "name": "_expand", "in": "query"}], "responses": {"200": {"description": "OK", "schema": {"allOf": [{"$ref": "#/definitions/daisy-server_api_v1.Response"}, {"type": "object", "properties": {"data": {"allOf": [{"$ref": "#/definitions/daisy-server_pkg_pagination.Result"}, {"type": "object", "properties": {"records": {"type": "array", "items": {"$ref": "#/definitions/daisy-server_api_v1.WmsDeliverSkuResponse"}}}}]}}}]}}}}, "post": {"security": [{"Bearer": []}], "description": "创建新的出库单物料规格记录", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["仓储模块", "出库单物料规格管理"], "summary": "创建出库单物料规格", "parameters": [{"description": "出库单物料规格信息", "name": "request", "in": "body", "required": true, "schema": {"$ref": "#/definitions/daisy-server_api_v1.WmsDeliverSkuCreateParams"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/daisy-server_api_v1.Response"}}}}, "delete": {"security": [{"Bearer": []}], "description": "批量删除指定IDs的出库单物料规格", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["仓储模块", "出库单物料规格管理"], "summary": "批量删除出库单物料规格", "parameters": [{"description": "出库单物料规格IDs", "name": "request", "in": "body", "required": true, "schema": {"$ref": "#/definitions/daisy-server_api_v1.BatchDeleteParams"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/daisy-server_api_v1.Response"}}}}}, "/wms/deliver-skus/{id}": {"get": {"security": [{"Bearer": []}], "description": "获取指定ID的出库单物料规格信息", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["仓储模块", "出库单物料规格管理"], "summary": "获取出库单物料规格", "parameters": [{"type": "integer", "description": "出库单物料规格ID", "name": "id", "in": "path", "required": true}, {"type": "string", "description": "展开关联信息，支持: deliver,item,sku", "name": "_expand", "in": "query"}], "responses": {"200": {"description": "OK", "schema": {"allOf": [{"$ref": "#/definitions/daisy-server_api_v1.Response"}, {"type": "object", "properties": {"data": {"$ref": "#/definitions/daisy-server_api_v1.WmsDeliverSkuResponse"}}}]}}}}, "delete": {"security": [{"Bearer": []}], "description": "删除指定ID的出库单物料规格", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["仓储模块", "出库单物料规格管理"], "summary": "删除出库单物料规格", "parameters": [{"type": "integer", "description": "出库单物料规格ID", "name": "id", "in": "path", "required": true}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/daisy-server_api_v1.Response"}}}}, "patch": {"security": [{"Bearer": []}], "description": "更新指定ID的出库单物料规格信息", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["仓储模块", "出库单物料规格管理"], "summary": "更新出库单物料规格", "parameters": [{"type": "integer", "description": "出库单物料规格ID", "name": "id", "in": "path", "required": true}, {"description": "出库单物料规格信息", "name": "request", "in": "body", "required": true, "schema": {"$ref": "#/definitions/daisy-server_api_v1.WmsDeliverSkuUpdateParams"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/daisy-server_api_v1.Response"}}}}}, "/wms/delivers": {"get": {"security": [{"Bearer": []}], "description": "分页获取出库单列表", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["仓储模块", "出库单管理"], "summary": "获取出库单列表", "parameters": [{"type": "integer", "description": "当前页码(默认值: 1)", "name": "_page", "in": "query"}, {"type": "integer", "description": "每页数量(默认值: 10)", "name": "_limit", "in": "query"}, {"type": "string", "description": "排序字段，多个字段用逗号分隔", "name": "_sort", "in": "query"}, {"type": "string", "description": "排序方式，asc或desc，多个方式用逗号分隔", "name": "_order", "in": "query"}, {"type": "string", "description": "全文搜索", "name": "q", "in": "query"}, {"type": "integer", "description": "状态筛选", "name": "status", "in": "query"}, {"type": "string", "description": "编号筛选", "name": "code", "in": "query"}, {"type": "integer", "description": "类型筛选", "name": "type", "in": "query"}, {"type": "integer", "description": "客户ID筛选", "name": "partnerId", "in": "query"}, {"type": "string", "description": "关联编号筛选", "name": "relatedNo", "in": "query"}, {"type": "string", "description": "展开关联信息，支持: partner", "name": "_expand", "in": "query"}], "responses": {"200": {"description": "OK", "schema": {"allOf": [{"$ref": "#/definitions/daisy-server_api_v1.Response"}, {"type": "object", "properties": {"data": {"allOf": [{"$ref": "#/definitions/daisy-server_pkg_pagination.Result"}, {"type": "object", "properties": {"records": {"type": "array", "items": {"$ref": "#/definitions/daisy-server_api_v1.WmsDeliverResponse"}}}}]}}}]}}}}, "post": {"security": [{"Bearer": []}], "description": "创建新的出库单记录", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["仓储模块", "出库单管理"], "summary": "创建出库单", "parameters": [{"description": "出库单信息", "name": "request", "in": "body", "required": true, "schema": {"$ref": "#/definitions/daisy-server_api_v1.WmsDeliverCreateParams"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/daisy-server_api_v1.Response"}}}}, "delete": {"security": [{"Bearer": []}], "description": "批量删除指定IDs的出库单", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["仓储模块", "出库单管理"], "summary": "批量删除出库单", "parameters": [{"description": "出库单IDs", "name": "request", "in": "body", "required": true, "schema": {"$ref": "#/definitions/daisy-server_api_v1.BatchDeleteParams"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/daisy-server_api_v1.Response"}}}}}, "/wms/delivers/{id}": {"get": {"security": [{"Bearer": []}], "description": "获取指定ID的出库单信息", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["仓储模块", "出库单管理"], "summary": "获取出库单", "parameters": [{"type": "integer", "description": "出库单ID", "name": "id", "in": "path", "required": true}, {"type": "string", "description": "展开关联信息，支持: partner", "name": "_expand", "in": "query"}], "responses": {"200": {"description": "OK", "schema": {"allOf": [{"$ref": "#/definitions/daisy-server_api_v1.Response"}, {"type": "object", "properties": {"data": {"$ref": "#/definitions/daisy-server_api_v1.WmsDeliverResponse"}}}]}}}}, "delete": {"security": [{"Bearer": []}], "description": "删除指定ID的出库单", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["仓储模块", "出库单管理"], "summary": "删除出库单", "parameters": [{"type": "integer", "description": "出库单ID", "name": "id", "in": "path", "required": true}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/daisy-server_api_v1.Response"}}}}, "patch": {"security": [{"Bearer": []}], "description": "更新指定ID的出库单信息", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["仓储模块", "出库单管理"], "summary": "更新出库单", "parameters": [{"type": "integer", "description": "出库单ID", "name": "id", "in": "path", "required": true}, {"description": "出库单信息", "name": "request", "in": "body", "required": true, "schema": {"$ref": "#/definitions/daisy-server_api_v1.WmsDeliverUpdateParams"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/daisy-server_api_v1.Response"}}}}}, "/wms/items": {"get": {"security": [{"Bearer": []}], "description": "分页获取物料列表", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["仓储模块", "物料管理"], "summary": "获取物料列表", "parameters": [{"type": "integer", "description": "当前页码(默认值: 1)", "name": "_page", "in": "query"}, {"type": "integer", "description": "每页数量(默认值: 10)", "name": "_limit", "in": "query"}, {"type": "string", "description": "排序字段，多个字段用逗号分隔", "name": "_sort", "in": "query"}, {"type": "string", "description": "排序方式，asc或desc，多个方式用逗号分隔", "name": "_order", "in": "query"}, {"type": "string", "description": "全文搜索", "name": "q", "in": "query"}, {"type": "boolean", "description": "状态筛选", "name": "status", "in": "query"}, {"type": "integer", "description": "分类ID筛选", "name": "metaId", "in": "query"}], "responses": {"200": {"description": "OK", "schema": {"allOf": [{"$ref": "#/definitions/daisy-server_api_v1.Response"}, {"type": "object", "properties": {"data": {"allOf": [{"$ref": "#/definitions/daisy-server_pkg_pagination.Result"}, {"type": "object", "properties": {"records": {"type": "array", "items": {"$ref": "#/definitions/daisy-server_api_v1.WmsItemResponse"}}}}]}}}]}}}}, "post": {"security": [{"Bearer": []}], "description": "创建新的物料记录", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["仓储模块", "物料管理"], "summary": "创建物料", "parameters": [{"description": "物料信息", "name": "request", "in": "body", "required": true, "schema": {"$ref": "#/definitions/daisy-server_api_v1.WmsItemCreateParams"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/daisy-server_api_v1.Response"}}}}, "delete": {"security": [{"Bearer": []}], "description": "批量删除指定IDs的物料", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["仓储模块", "物料管理"], "summary": "批量删除物料", "parameters": [{"description": "物料IDs", "name": "request", "in": "body", "required": true, "schema": {"$ref": "#/definitions/daisy-server_api_v1.BatchDeleteParams"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/daisy-server_api_v1.Response"}}}}}, "/wms/items/{id}": {"get": {"security": [{"Bearer": []}], "description": "获取指定ID的物料信息", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["仓储模块", "物料管理"], "summary": "获取物料", "parameters": [{"type": "integer", "description": "物料ID", "name": "id", "in": "path", "required": true}], "responses": {"200": {"description": "OK", "schema": {"allOf": [{"$ref": "#/definitions/daisy-server_api_v1.Response"}, {"type": "object", "properties": {"data": {"$ref": "#/definitions/daisy-server_api_v1.WmsItemResponse"}}}]}}}}, "delete": {"security": [{"Bearer": []}], "description": "删除指定ID的物料", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["仓储模块", "物料管理"], "summary": "删除物料", "parameters": [{"type": "integer", "description": "物料ID", "name": "id", "in": "path", "required": true}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/daisy-server_api_v1.Response"}}}}, "patch": {"security": [{"Bearer": []}], "description": "更新指定ID的物料信息", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["仓储模块", "物料管理"], "summary": "更新物料", "parameters": [{"type": "integer", "description": "物料ID", "name": "id", "in": "path", "required": true}, {"description": "物料信息", "name": "request", "in": "body", "required": true, "schema": {"$ref": "#/definitions/daisy-server_api_v1.WmsItemUpdateParams"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/daisy-server_api_v1.Response"}}}}}, "/wms/logs": {"get": {"security": [{"Bearer": []}], "description": "分页获取库存日志列表", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["仓储模块", "库存日志管理"], "summary": "获取库存日志列表", "parameters": [{"type": "integer", "description": "当前页码(默认值: 1)", "name": "_page", "in": "query"}, {"type": "integer", "description": "每页数量(默认值: 10)", "name": "_limit", "in": "query"}, {"type": "string", "description": "排序字段，多个字段用逗号分隔", "name": "_sort", "in": "query"}, {"type": "string", "description": "排序方式，asc或desc，多个方式用逗号分隔", "name": "_order", "in": "query"}, {"type": "string", "description": "全文搜索", "name": "q", "in": "query"}, {"type": "integer", "description": "物料ID筛选", "name": "itemId", "in": "query"}, {"type": "integer", "description": "规格ID筛选", "name": "skuId", "in": "query"}, {"type": "integer", "description": "类型筛选 1-入库 2-出库 3-调拨 4-盘点 5-其它", "name": "type", "in": "query"}, {"type": "string", "description": "关联单号筛选", "name": "relatedNo", "in": "query"}, {"type": "string", "description": "展开关联信息，支持: item,sku", "name": "_expand", "in": "query"}], "responses": {"200": {"description": "OK", "schema": {"allOf": [{"$ref": "#/definitions/daisy-server_api_v1.Response"}, {"type": "object", "properties": {"data": {"allOf": [{"$ref": "#/definitions/daisy-server_pkg_pagination.Result"}, {"type": "object", "properties": {"records": {"type": "array", "items": {"$ref": "#/definitions/daisy-server_api_v1.WmsLogResponse"}}}}]}}}]}}}}, "post": {"security": [{"Bearer": []}], "description": "创建新的库存日志记录", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["仓储模块", "库存日志管理"], "summary": "创建库存日志", "parameters": [{"description": "库存日志信息", "name": "request", "in": "body", "required": true, "schema": {"$ref": "#/definitions/daisy-server_api_v1.WmsLogCreateParams"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/daisy-server_api_v1.Response"}}}}, "delete": {"security": [{"Bearer": []}], "description": "批量删除指定IDs的库存日志", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["仓储模块", "库存日志管理"], "summary": "批量删除库存日志", "parameters": [{"description": "库存日志IDs", "name": "request", "in": "body", "required": true, "schema": {"$ref": "#/definitions/daisy-server_api_v1.BatchDeleteParams"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/daisy-server_api_v1.Response"}}}}}, "/wms/logs/{id}": {"get": {"security": [{"Bearer": []}], "description": "获取指定ID的库存日志信息", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["仓储模块", "库存日志管理"], "summary": "获取库存日志", "parameters": [{"type": "integer", "description": "库存日志ID", "name": "id", "in": "path", "required": true}, {"type": "string", "description": "展开关联信息，支持: item,sku", "name": "_expand", "in": "query"}], "responses": {"200": {"description": "OK", "schema": {"allOf": [{"$ref": "#/definitions/daisy-server_api_v1.Response"}, {"type": "object", "properties": {"data": {"$ref": "#/definitions/daisy-server_api_v1.WmsLogResponse"}}}]}}}}, "delete": {"security": [{"Bearer": []}], "description": "删除指定ID的库存日志", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["仓储模块", "库存日志管理"], "summary": "删除库存日志", "parameters": [{"type": "integer", "description": "库存日志ID", "name": "id", "in": "path", "required": true}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/daisy-server_api_v1.Response"}}}}, "patch": {"security": [{"Bearer": []}], "description": "更新指定ID的库存日志信息", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["仓储模块", "库存日志管理"], "summary": "更新库存日志", "parameters": [{"type": "integer", "description": "库存日志ID", "name": "id", "in": "path", "required": true}, {"description": "库存日志信息", "name": "request", "in": "body", "required": true, "schema": {"$ref": "#/definitions/daisy-server_api_v1.WmsLogUpdateParams"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/daisy-server_api_v1.Response"}}}}}, "/wms/metas": {"get": {"security": [{"Bearer": []}], "description": "分页获取物料分类列表", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["仓储模块", "物料分类管理"], "summary": "获取物料分类列表", "parameters": [{"type": "integer", "description": "当前页码(默认值: 1)", "name": "_page", "in": "query"}, {"type": "integer", "description": "每页数量(默认值: 10)", "name": "_limit", "in": "query"}, {"type": "string", "description": "排序字段，多个字段用逗号分隔", "name": "_sort", "in": "query"}, {"type": "string", "description": "排序方式，asc或desc，多个方式用逗号分隔", "name": "_order", "in": "query"}, {"type": "string", "description": "全文搜索", "name": "q", "in": "query"}, {"type": "boolean", "description": "状态筛选", "name": "status", "in": "query"}, {"type": "integer", "description": "父级ID筛选", "name": "parentId", "in": "query"}], "responses": {"200": {"description": "OK", "schema": {"allOf": [{"$ref": "#/definitions/daisy-server_api_v1.Response"}, {"type": "object", "properties": {"data": {"allOf": [{"$ref": "#/definitions/daisy-server_pkg_pagination.Result"}, {"type": "object", "properties": {"records": {"type": "array", "items": {"$ref": "#/definitions/daisy-server_api_v1.WmsMetaResponse"}}}}]}}}]}}}}, "post": {"security": [{"Bearer": []}], "description": "创建新的物料分类记录", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["仓储模块", "物料分类管理"], "summary": "创建物料分类", "parameters": [{"description": "物料分类信息", "name": "request", "in": "body", "required": true, "schema": {"$ref": "#/definitions/daisy-server_api_v1.WmsMetaCreateParams"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/daisy-server_api_v1.Response"}}}}, "delete": {"security": [{"Bearer": []}], "description": "批量删除指定IDs的物料分类", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["仓储模块", "物料分类管理"], "summary": "批量删除物料分类", "parameters": [{"description": "物料分类IDs", "name": "request", "in": "body", "required": true, "schema": {"$ref": "#/definitions/daisy-server_api_v1.BatchDeleteParams"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/daisy-server_api_v1.Response"}}}}}, "/wms/metas/{id}": {"get": {"security": [{"Bearer": []}], "description": "获取指定ID的物料分类信息", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["仓储模块", "物料分类管理"], "summary": "获取物料分类", "parameters": [{"type": "integer", "description": "物料分类ID", "name": "id", "in": "path", "required": true}], "responses": {"200": {"description": "OK", "schema": {"allOf": [{"$ref": "#/definitions/daisy-server_api_v1.Response"}, {"type": "object", "properties": {"data": {"$ref": "#/definitions/daisy-server_api_v1.WmsMetaResponse"}}}]}}}}, "delete": {"security": [{"Bearer": []}], "description": "删除指定ID的物料分类", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["仓储模块", "物料分类管理"], "summary": "删除物料分类", "parameters": [{"type": "integer", "description": "物料分类ID", "name": "id", "in": "path", "required": true}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/daisy-server_api_v1.Response"}}}}, "patch": {"security": [{"Bearer": []}], "description": "更新指定ID的物料分类信息", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["仓储模块", "物料分类管理"], "summary": "更新物料分类", "parameters": [{"type": "integer", "description": "物料分类ID", "name": "id", "in": "path", "required": true}, {"description": "物料分类信息", "name": "request", "in": "body", "required": true, "schema": {"$ref": "#/definitions/daisy-server_api_v1.WmsMetaUpdateParams"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/daisy-server_api_v1.Response"}}}}}, "/wms/packs": {"get": {"security": [{"Bearer": []}], "description": "分页获取包装单列表", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["仓储模块", "包装单管理"], "summary": "获取包装单列表", "parameters": [{"type": "integer", "description": "当前页码(默认值: 1)", "name": "_page", "in": "query"}, {"type": "integer", "description": "每页数量(默认值: 10)", "name": "_limit", "in": "query"}, {"type": "string", "description": "排序字段，多个字段用逗号分隔", "name": "_sort", "in": "query"}, {"type": "string", "description": "排序方式，asc或desc，多个方式用逗号分隔", "name": "_order", "in": "query"}, {"type": "string", "description": "全文搜索", "name": "q", "in": "query"}, {"type": "string", "description": "编号筛选", "name": "code", "in": "query"}, {"type": "integer", "description": "出库单ID筛选", "name": "deliverId", "in": "query"}, {"type": "integer", "description": "物料ID筛选", "name": "itemId", "in": "query"}, {"type": "integer", "description": "规格ID筛选", "name": "skuId", "in": "query"}, {"type": "string", "description": "展开关联信息，支持: deliver,item,sku", "name": "_expand", "in": "query"}], "responses": {"200": {"description": "OK", "schema": {"allOf": [{"$ref": "#/definitions/daisy-server_api_v1.Response"}, {"type": "object", "properties": {"data": {"allOf": [{"$ref": "#/definitions/daisy-server_pkg_pagination.Result"}, {"type": "object", "properties": {"records": {"type": "array", "items": {"$ref": "#/definitions/daisy-server_api_v1.WmsPackResponse"}}}}]}}}]}}}}, "post": {"security": [{"Bearer": []}], "description": "创建新的包装单记录", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["仓储模块", "包装单管理"], "summary": "创建包装单", "parameters": [{"description": "包装单信息", "name": "request", "in": "body", "required": true, "schema": {"$ref": "#/definitions/daisy-server_api_v1.WmsPackCreateParams"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/daisy-server_api_v1.Response"}}}}, "delete": {"security": [{"Bearer": []}], "description": "批量删除指定IDs的包装单", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["仓储模块", "包装单管理"], "summary": "批量删除包装单", "parameters": [{"description": "包装单IDs", "name": "request", "in": "body", "required": true, "schema": {"$ref": "#/definitions/daisy-server_api_v1.BatchDeleteParams"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/daisy-server_api_v1.Response"}}}}}, "/wms/packs/{id}": {"get": {"security": [{"Bearer": []}], "description": "获取指定ID的包装单信息", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["仓储模块", "包装单管理"], "summary": "获取包装单", "parameters": [{"type": "integer", "description": "包装单ID", "name": "id", "in": "path", "required": true}, {"type": "string", "description": "展开关联信息，支持: deliver,item,sku", "name": "_expand", "in": "query"}], "responses": {"200": {"description": "OK", "schema": {"allOf": [{"$ref": "#/definitions/daisy-server_api_v1.Response"}, {"type": "object", "properties": {"data": {"$ref": "#/definitions/daisy-server_api_v1.WmsPackResponse"}}}]}}}}, "delete": {"security": [{"Bearer": []}], "description": "删除指定ID的包装单", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["仓储模块", "包装单管理"], "summary": "删除包装单", "parameters": [{"type": "integer", "description": "包装单ID", "name": "id", "in": "path", "required": true}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/daisy-server_api_v1.Response"}}}}, "patch": {"security": [{"Bearer": []}], "description": "更新指定ID的包装单信息", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["仓储模块", "包装单管理"], "summary": "更新包装单", "parameters": [{"type": "integer", "description": "包装单ID", "name": "id", "in": "path", "required": true}, {"description": "包装单信息", "name": "request", "in": "body", "required": true, "schema": {"$ref": "#/definitions/daisy-server_api_v1.WmsPackUpdateParams"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/daisy-server_api_v1.Response"}}}}}, "/wms/partners": {"get": {"security": [{"Bearer": []}], "description": "分页获取往来单位列表", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["仓储模块", "往来单位管理"], "summary": "获取往来单位列表", "parameters": [{"type": "integer", "description": "当前页码(默认值: 1)", "name": "_page", "in": "query"}, {"type": "integer", "description": "每页数量(默认值: 10)", "name": "_limit", "in": "query"}, {"type": "string", "description": "排序字段，多个字段用逗号分隔", "name": "_sort", "in": "query"}, {"type": "string", "description": "排序方式，asc或desc，多个方式用逗号分隔", "name": "_order", "in": "query"}, {"type": "string", "description": "全文搜索", "name": "q", "in": "query"}, {"type": "boolean", "description": "状态筛选", "name": "status", "in": "query"}, {"type": "string", "description": "类型筛选", "name": "type", "in": "query"}, {"type": "integer", "description": "级别筛选", "name": "level", "in": "query"}], "responses": {"200": {"description": "OK", "schema": {"allOf": [{"$ref": "#/definitions/daisy-server_api_v1.Response"}, {"type": "object", "properties": {"data": {"allOf": [{"$ref": "#/definitions/daisy-server_pkg_pagination.Result"}, {"type": "object", "properties": {"records": {"type": "array", "items": {"$ref": "#/definitions/daisy-server_api_v1.WmsPartnerResponse"}}}}]}}}]}}}}, "post": {"security": [{"Bearer": []}], "description": "创建新的往来单位记录", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["仓储模块", "往来单位管理"], "summary": "创建往来单位", "parameters": [{"description": "往来单位信息", "name": "request", "in": "body", "required": true, "schema": {"$ref": "#/definitions/daisy-server_api_v1.WmsPartnerCreateParams"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/daisy-server_api_v1.Response"}}}}, "delete": {"security": [{"Bearer": []}], "description": "批量删除指定IDs的往来单位", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["仓储模块", "往来单位管理"], "summary": "批量删除往来单位", "parameters": [{"description": "往来单位IDs", "name": "request", "in": "body", "required": true, "schema": {"$ref": "#/definitions/daisy-server_api_v1.BatchDeleteParams"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/daisy-server_api_v1.Response"}}}}}, "/wms/partners/{id}": {"get": {"security": [{"Bearer": []}], "description": "获取指定ID的往来单位信息", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["仓储模块", "往来单位管理"], "summary": "获取往来单位", "parameters": [{"type": "integer", "description": "往来单位ID", "name": "id", "in": "path", "required": true}], "responses": {"200": {"description": "OK", "schema": {"allOf": [{"$ref": "#/definitions/daisy-server_api_v1.Response"}, {"type": "object", "properties": {"data": {"$ref": "#/definitions/daisy-server_api_v1.WmsPartnerResponse"}}}]}}}}, "delete": {"security": [{"Bearer": []}], "description": "删除指定ID的往来单位", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["仓储模块", "往来单位管理"], "summary": "删除往来单位", "parameters": [{"type": "integer", "description": "往来单位ID", "name": "id", "in": "path", "required": true}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/daisy-server_api_v1.Response"}}}}, "patch": {"security": [{"Bearer": []}], "description": "更新指定ID的往来单位信息", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["仓储模块", "往来单位管理"], "summary": "更新往来单位", "parameters": [{"type": "integer", "description": "往来单位ID", "name": "id", "in": "path", "required": true}, {"description": "往来单位信息", "name": "request", "in": "body", "required": true, "schema": {"$ref": "#/definitions/daisy-server_api_v1.WmsPartnerUpdateParams"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/daisy-server_api_v1.Response"}}}}}, "/wms/receive-skus": {"get": {"security": [{"Bearer": []}], "description": "分页获取入库单物料规格列表", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["仓储模块", "入库单物料规格管理"], "summary": "获取入库单物料规格列表", "parameters": [{"type": "integer", "description": "当前页码(默认值: 1)", "name": "_page", "in": "query"}, {"type": "integer", "description": "每页数量(默认值: 10)", "name": "_limit", "in": "query"}, {"type": "string", "description": "排序字段，多个字段用逗号分隔", "name": "_sort", "in": "query"}, {"type": "string", "description": "排序方式，asc或desc，多个方式用逗号分隔", "name": "_order", "in": "query"}, {"type": "string", "description": "全文搜索", "name": "q", "in": "query"}, {"type": "boolean", "description": "状态筛选", "name": "status", "in": "query"}, {"type": "integer", "description": "入库单ID筛选", "name": "receiveId", "in": "query"}, {"type": "integer", "description": "物料ID筛选", "name": "itemId", "in": "query"}, {"type": "integer", "description": "规格ID筛选", "name": "skuId", "in": "query"}, {"type": "string", "description": "批次号筛选", "name": "batchNo", "in": "query"}, {"type": "string", "description": "展开关联信息，支持: receive,item,sku", "name": "_expand", "in": "query"}], "responses": {"200": {"description": "OK", "schema": {"allOf": [{"$ref": "#/definitions/daisy-server_api_v1.Response"}, {"type": "object", "properties": {"data": {"allOf": [{"$ref": "#/definitions/daisy-server_pkg_pagination.Result"}, {"type": "object", "properties": {"records": {"type": "array", "items": {"$ref": "#/definitions/daisy-server_api_v1.WmsReceiveSkuResponse"}}}}]}}}]}}}}, "post": {"security": [{"Bearer": []}], "description": "创建新的入库单物料规格记录", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["仓储模块", "入库单物料规格管理"], "summary": "创建入库单物料规格", "parameters": [{"description": "入库单物料规格信息", "name": "request", "in": "body", "required": true, "schema": {"$ref": "#/definitions/daisy-server_api_v1.WmsReceiveSkuCreateParams"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/daisy-server_api_v1.Response"}}}}, "delete": {"security": [{"Bearer": []}], "description": "批量删除指定IDs的入库单物料规格", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["仓储模块", "入库单物料规格管理"], "summary": "批量删除入库单物料规格", "parameters": [{"description": "入库单物料规格IDs", "name": "request", "in": "body", "required": true, "schema": {"$ref": "#/definitions/daisy-server_api_v1.BatchDeleteParams"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/daisy-server_api_v1.Response"}}}}}, "/wms/receive-skus/{id}": {"get": {"security": [{"Bearer": []}], "description": "获取指定ID的入库单物料规格信息", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["仓储模块", "入库单物料规格管理"], "summary": "获取入库单物料规格", "parameters": [{"type": "integer", "description": "入库单物料规格ID", "name": "id", "in": "path", "required": true}, {"type": "string", "description": "展开关联信息，支持: receive,item,sku", "name": "_expand", "in": "query"}], "responses": {"200": {"description": "OK", "schema": {"allOf": [{"$ref": "#/definitions/daisy-server_api_v1.Response"}, {"type": "object", "properties": {"data": {"$ref": "#/definitions/daisy-server_api_v1.WmsReceiveSkuResponse"}}}]}}}}, "delete": {"security": [{"Bearer": []}], "description": "删除指定ID的入库单物料规格", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["仓储模块", "入库单物料规格管理"], "summary": "删除入库单物料规格", "parameters": [{"type": "integer", "description": "入库单物料规格ID", "name": "id", "in": "path", "required": true}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/daisy-server_api_v1.Response"}}}}, "patch": {"security": [{"Bearer": []}], "description": "更新指定ID的入库单物料规格信息", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["仓储模块", "入库单物料规格管理"], "summary": "更新入库单物料规格", "parameters": [{"type": "integer", "description": "入库单物料规格ID", "name": "id", "in": "path", "required": true}, {"description": "入库单物料规格信息", "name": "request", "in": "body", "required": true, "schema": {"$ref": "#/definitions/daisy-server_api_v1.WmsReceiveSkuUpdateParams"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/daisy-server_api_v1.Response"}}}}}, "/wms/receives": {"get": {"security": [{"Bearer": []}], "description": "分页获取入库单列表", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["仓储模块", "入库单管理"], "summary": "获取入库单列表", "parameters": [{"type": "integer", "description": "当前页码(默认值: 1)", "name": "_page", "in": "query"}, {"type": "integer", "description": "每页数量(默认值: 10)", "name": "_limit", "in": "query"}, {"type": "string", "description": "排序字段，多个字段用逗号分隔", "name": "_sort", "in": "query"}, {"type": "string", "description": "排序方式，asc或desc，多个方式用逗号分隔", "name": "_order", "in": "query"}, {"type": "string", "description": "全文搜索", "name": "q", "in": "query"}, {"type": "integer", "description": "状态筛选 1草稿 2待审核 3作业中 4已完成 5已作废", "name": "status", "in": "query"}, {"type": "string", "description": "编号筛选", "name": "code", "in": "query"}, {"type": "integer", "description": "类型筛选", "name": "type", "in": "query"}, {"type": "integer", "description": "供应商ID筛选", "name": "partnerId", "in": "query"}, {"type": "string", "description": "关联编号筛选", "name": "relatedNo", "in": "query"}, {"type": "string", "description": "展开关联信息，支持: partner", "name": "_expand", "in": "query"}], "responses": {"200": {"description": "OK", "schema": {"allOf": [{"$ref": "#/definitions/daisy-server_api_v1.Response"}, {"type": "object", "properties": {"data": {"allOf": [{"$ref": "#/definitions/daisy-server_pkg_pagination.Result"}, {"type": "object", "properties": {"records": {"type": "array", "items": {"$ref": "#/definitions/daisy-server_api_v1.WmsReceiveResponse"}}}}]}}}]}}}}, "post": {"security": [{"Bearer": []}], "description": "创建新的入库单记录", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["仓储模块", "入库单管理"], "summary": "创建入库单", "parameters": [{"description": "入库单信息", "name": "request", "in": "body", "required": true, "schema": {"$ref": "#/definitions/daisy-server_api_v1.WmsReceiveCreateParams"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/daisy-server_api_v1.Response"}}}}, "delete": {"security": [{"Bearer": []}], "description": "批量删除指定IDs的入库单", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["仓储模块", "入库单管理"], "summary": "批量删除入库单", "parameters": [{"description": "入库单IDs", "name": "request", "in": "body", "required": true, "schema": {"$ref": "#/definitions/daisy-server_api_v1.BatchDeleteParams"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/daisy-server_api_v1.Response"}}}}}, "/wms/receives/{id}": {"get": {"security": [{"Bearer": []}], "description": "获取指定ID的入库单信息", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["仓储模块", "入库单管理"], "summary": "获取入库单", "parameters": [{"type": "integer", "description": "入库单ID", "name": "id", "in": "path", "required": true}, {"type": "string", "description": "展开关联信息，支持: partner", "name": "_expand", "in": "query"}], "responses": {"200": {"description": "OK", "schema": {"allOf": [{"$ref": "#/definitions/daisy-server_api_v1.Response"}, {"type": "object", "properties": {"data": {"$ref": "#/definitions/daisy-server_api_v1.WmsReceiveResponse"}}}]}}}}, "delete": {"security": [{"Bearer": []}], "description": "删除指定ID的入库单", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["仓储模块", "入库单管理"], "summary": "删除入库单", "parameters": [{"type": "integer", "description": "入库单ID", "name": "id", "in": "path", "required": true}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/daisy-server_api_v1.Response"}}}}, "patch": {"security": [{"Bearer": []}], "description": "更新指定ID的入库单信息", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["仓储模块", "入库单管理"], "summary": "更新入库单", "parameters": [{"type": "integer", "description": "入库单ID", "name": "id", "in": "path", "required": true}, {"description": "入库单信息", "name": "request", "in": "body", "required": true, "schema": {"$ref": "#/definitions/daisy-server_api_v1.WmsReceiveUpdateParams"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/daisy-server_api_v1.Response"}}}}}, "/wms/skus": {"get": {"security": [{"Bearer": []}], "description": "分页获取物料规格列表", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["仓储模块", "物料规格管理"], "summary": "获取物料规格列表", "parameters": [{"type": "integer", "description": "当前页码(默认值: 1)", "name": "_page", "in": "query"}, {"type": "integer", "description": "每页数量(默认值: 10)", "name": "_limit", "in": "query"}, {"type": "string", "description": "排序字段，多个字段用逗号分隔", "name": "_sort", "in": "query"}, {"type": "string", "description": "排序方式，asc或desc，多个方式用逗号分隔", "name": "_order", "in": "query"}, {"type": "string", "description": "全文搜索", "name": "q", "in": "query"}, {"type": "boolean", "description": "状态筛选", "name": "status", "in": "query"}, {"type": "integer", "description": "物料ID筛选", "name": "itemId", "in": "query"}, {"type": "string", "description": "单位筛选", "name": "unit", "in": "query"}, {"type": "number", "description": "最小库存筛选", "name": "min", "in": "query"}], "responses": {"200": {"description": "OK", "schema": {"allOf": [{"$ref": "#/definitions/daisy-server_api_v1.Response"}, {"type": "object", "properties": {"data": {"allOf": [{"$ref": "#/definitions/daisy-server_pkg_pagination.Result"}, {"type": "object", "properties": {"records": {"type": "array", "items": {"$ref": "#/definitions/daisy-server_api_v1.WmsSkuResponse"}}}}]}}}]}}}}, "post": {"security": [{"Bearer": []}], "description": "创建新的物料规格记录", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["仓储模块", "物料规格管理"], "summary": "创建物料规格", "parameters": [{"description": "物料规格信息", "name": "request", "in": "body", "required": true, "schema": {"$ref": "#/definitions/daisy-server_api_v1.WmsSkuCreateParams"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/daisy-server_api_v1.Response"}}}}, "delete": {"security": [{"Bearer": []}], "description": "批量删除指定IDs的物料规格", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["仓储模块", "物料规格管理"], "summary": "批量删除物料规格", "parameters": [{"description": "物料规格IDs", "name": "request", "in": "body", "required": true, "schema": {"$ref": "#/definitions/daisy-server_api_v1.BatchDeleteParams"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/daisy-server_api_v1.Response"}}}}}, "/wms/skus/{id}": {"get": {"security": [{"Bearer": []}], "description": "获取指定ID的物料规格信息", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["仓储模块", "物料规格管理"], "summary": "获取物料规格", "parameters": [{"type": "integer", "description": "物料规格ID", "name": "id", "in": "path", "required": true}], "responses": {"200": {"description": "OK", "schema": {"allOf": [{"$ref": "#/definitions/daisy-server_api_v1.Response"}, {"type": "object", "properties": {"data": {"$ref": "#/definitions/daisy-server_api_v1.WmsSkuResponse"}}}]}}}}, "delete": {"security": [{"Bearer": []}], "description": "删除指定ID的物料规格", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["仓储模块", "物料规格管理"], "summary": "删除物料规格", "parameters": [{"type": "integer", "description": "物料规格ID", "name": "id", "in": "path", "required": true}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/daisy-server_api_v1.Response"}}}}, "patch": {"security": [{"Bearer": []}], "description": "更新指定ID的物料规格信息", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["仓储模块", "物料规格管理"], "summary": "更新物料规格", "parameters": [{"type": "integer", "description": "物料规格ID", "name": "id", "in": "path", "required": true}, {"description": "物料规格信息", "name": "request", "in": "body", "required": true, "schema": {"$ref": "#/definitions/daisy-server_api_v1.WmsSkuUpdateParams"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/daisy-server_api_v1.Response"}}}}}, "/wms/staffs": {"get": {"security": [{"Bearer": []}], "description": "分页获取员工列表", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["仓储模块", "员工管理"], "summary": "获取员工列表", "parameters": [{"type": "integer", "description": "当前页码(默认值: 1)", "name": "_page", "in": "query"}, {"type": "integer", "description": "每页数量(默认值: 10)", "name": "_limit", "in": "query"}, {"type": "string", "description": "排序字段，多个字段用逗号分隔", "name": "_sort", "in": "query"}, {"type": "string", "description": "排序方式，asc或desc，多个方式用逗号分隔", "name": "_order", "in": "query"}, {"type": "string", "description": "全文搜索", "name": "q", "in": "query"}, {"type": "boolean", "description": "状态筛选", "name": "status", "in": "query"}, {"type": "string", "description": "员工名筛选", "name": "username", "in": "query"}, {"type": "string", "description": "昵称筛选", "name": "nickname", "in": "query"}, {"type": "string", "description": "手机号筛选", "name": "phone", "in": "query"}, {"type": "string", "description": "邮箱筛选", "name": "email", "in": "query"}, {"type": "string", "description": "展开关联信息，支持: tenant", "name": "_expand", "in": "query"}], "responses": {"200": {"description": "OK", "schema": {"allOf": [{"$ref": "#/definitions/daisy-server_api_v1.Response"}, {"type": "object", "properties": {"data": {"allOf": [{"$ref": "#/definitions/daisy-server_pkg_pagination.Result"}, {"type": "object", "properties": {"records": {"type": "array", "items": {"$ref": "#/definitions/daisy-server_api_v1.WmsStaffResponse"}}}}]}}}]}}}}, "post": {"security": [{"Bearer": []}], "description": "创建新的员工", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["仓储模块", "员工管理"], "summary": "创建员工", "parameters": [{"description": "员工信息", "name": "request", "in": "body", "required": true, "schema": {"$ref": "#/definitions/daisy-server_api_v1.WmsStaffCreateParams"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/daisy-server_api_v1.Response"}}}}, "delete": {"security": [{"Bearer": []}], "description": "批量删除指定IDs的员工", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["仓储模块", "员工管理"], "summary": "批量删除员工", "parameters": [{"description": "员工IDs", "name": "request", "in": "body", "required": true, "schema": {"$ref": "#/definitions/daisy-server_api_v1.BatchDeleteParams"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/daisy-server_api_v1.Response"}}}}}, "/wms/staffs/{id}": {"get": {"security": [{"Bearer": []}], "description": "获取指定ID的员工信息", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["仓储模块", "员工管理"], "summary": "获取员工", "parameters": [{"type": "integer", "description": "员工ID", "name": "id", "in": "path", "required": true}, {"type": "string", "description": "展开关联信息，支持: tenant", "name": "_expand", "in": "query"}], "responses": {"200": {"description": "OK", "schema": {"allOf": [{"$ref": "#/definitions/daisy-server_api_v1.Response"}, {"type": "object", "properties": {"data": {"$ref": "#/definitions/daisy-server_api_v1.WmsStaffResponse"}}}]}}}}, "delete": {"security": [{"Bearer": []}], "description": "删除指定ID的员工", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["仓储模块", "员工管理"], "summary": "删除员工", "parameters": [{"type": "integer", "description": "员工ID", "name": "id", "in": "path", "required": true}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/daisy-server_api_v1.Response"}}}}, "patch": {"security": [{"Bearer": []}], "description": "更新指定ID的员工信息", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["仓储模块", "员工管理"], "summary": "更新员工", "parameters": [{"type": "integer", "description": "员工ID", "name": "id", "in": "path", "required": true}, {"description": "员工信息", "name": "request", "in": "body", "required": true, "schema": {"$ref": "#/definitions/daisy-server_api_v1.WmsStaffUpdateParams"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/daisy-server_api_v1.Response"}}}}}, "/wms/stocks": {"get": {"security": [{"Bearer": []}], "description": "分页获取库存记录列表", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["仓储模块", "库存管理"], "summary": "获取库存记录列表", "parameters": [{"type": "integer", "description": "当前页码(默认值: 1)", "name": "_page", "in": "query"}, {"type": "integer", "description": "每页数量(默认值: 10)", "name": "_limit", "in": "query"}, {"type": "string", "description": "排序字段，多个字段用逗号分隔", "name": "_sort", "in": "query"}, {"type": "string", "description": "排序方式，asc或desc，多个方式用逗号分隔", "name": "_order", "in": "query"}, {"type": "string", "description": "全文搜索", "name": "q", "in": "query"}, {"type": "integer", "description": "物料ID筛选", "name": "itemId", "in": "query"}, {"type": "integer", "description": "物料规格ID筛选", "name": "skuId", "in": "query"}, {"type": "string", "description": "库位ID筛选", "name": "areaId", "in": "query"}, {"type": "string", "description": "展开关联信息，支持: item,sku", "name": "_expand", "in": "query"}], "responses": {"200": {"description": "OK", "schema": {"allOf": [{"$ref": "#/definitions/daisy-server_api_v1.Response"}, {"type": "object", "properties": {"data": {"allOf": [{"$ref": "#/definitions/daisy-server_pkg_pagination.Result"}, {"type": "object", "properties": {"records": {"type": "array", "items": {"$ref": "#/definitions/daisy-server_api_v1.WmsStockResponse"}}}}]}}}]}}}}, "post": {"security": [{"Bearer": []}], "description": "创建新的库存记录", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["仓储模块", "库存管理"], "summary": "创建库存记录", "parameters": [{"description": "库存信息", "name": "request", "in": "body", "required": true, "schema": {"$ref": "#/definitions/daisy-server_api_v1.WmsStockCreateParams"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/daisy-server_api_v1.Response"}}}}, "delete": {"security": [{"Bearer": []}], "description": "批量删除指定IDs的库存记录", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["仓储模块", "库存管理"], "summary": "批量删除库存记录", "parameters": [{"description": "库存记录IDs", "name": "request", "in": "body", "required": true, "schema": {"$ref": "#/definitions/daisy-server_api_v1.BatchDeleteParams"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/daisy-server_api_v1.Response"}}}}}, "/wms/stocks/{id}": {"get": {"security": [{"Bearer": []}], "description": "获取指定ID的库存记录信息", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["仓储模块", "库存管理"], "summary": "获取库存记录", "parameters": [{"type": "integer", "description": "库存记录ID", "name": "id", "in": "path", "required": true}, {"type": "string", "description": "展开关联信息，支持: item,sku", "name": "_expand", "in": "query"}], "responses": {"200": {"description": "OK", "schema": {"allOf": [{"$ref": "#/definitions/daisy-server_api_v1.Response"}, {"type": "object", "properties": {"data": {"$ref": "#/definitions/daisy-server_api_v1.WmsStockResponse"}}}]}}}}, "delete": {"security": [{"Bearer": []}], "description": "删除指定ID的库存记录", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["仓储模块", "库存管理"], "summary": "删除库存记录", "parameters": [{"type": "integer", "description": "库存记录ID", "name": "id", "in": "path", "required": true}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/daisy-server_api_v1.Response"}}}}, "patch": {"security": [{"Bearer": []}], "description": "更新指定ID的库存记录信息", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["仓储模块", "库存管理"], "summary": "更新库存记录", "parameters": [{"type": "integer", "description": "库存记录ID", "name": "id", "in": "path", "required": true}, {"description": "库存信息", "name": "request", "in": "body", "required": true, "schema": {"$ref": "#/definitions/daisy-server_api_v1.WmsStockUpdateParams"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/daisy-server_api_v1.Response"}}}}}, "/wms/transfer-skus": {"get": {"security": [{"Bearer": []}], "description": "分页获取调拨单物料规格列表", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["仓储模块", "调拨单物料规格管理"], "summary": "获取调拨单物料规格列表", "parameters": [{"type": "integer", "description": "当前页码(默认值: 1)", "name": "_page", "in": "query"}, {"type": "integer", "description": "每页数量(默认值: 10)", "name": "_limit", "in": "query"}, {"type": "string", "description": "排序字段，多个字段用逗号分隔", "name": "_sort", "in": "query"}, {"type": "string", "description": "排序方式，asc或desc，多个方式用逗号分隔", "name": "_order", "in": "query"}, {"type": "string", "description": "全文搜索", "name": "q", "in": "query"}, {"type": "boolean", "description": "状态筛选", "name": "status", "in": "query"}, {"type": "integer", "description": "调拨单ID筛选", "name": "transferId", "in": "query"}, {"type": "integer", "description": "物料ID筛选", "name": "itemId", "in": "query"}, {"type": "integer", "description": "规格ID筛选", "name": "skuId", "in": "query"}, {"type": "string", "description": "展开关联信息，支持: transfer,item,sku", "name": "_expand", "in": "query"}], "responses": {"200": {"description": "OK", "schema": {"allOf": [{"$ref": "#/definitions/daisy-server_api_v1.Response"}, {"type": "object", "properties": {"data": {"allOf": [{"$ref": "#/definitions/daisy-server_pkg_pagination.Result"}, {"type": "object", "properties": {"records": {"type": "array", "items": {"$ref": "#/definitions/daisy-server_api_v1.WmsTransferSkuResponse"}}}}]}}}]}}}}, "post": {"security": [{"Bearer": []}], "description": "创建新的调拨单物料规格记录", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["仓储模块", "调拨单物料规格管理"], "summary": "创建调拨单物料规格", "parameters": [{"description": "调拨单物料规格信息", "name": "request", "in": "body", "required": true, "schema": {"$ref": "#/definitions/daisy-server_api_v1.WmsTransferSkuCreateParams"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/daisy-server_api_v1.Response"}}}}, "delete": {"security": [{"Bearer": []}], "description": "批量删除指定IDs的调拨单物料规格", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["仓储模块", "调拨单物料规格管理"], "summary": "批量删除调拨单物料规格", "parameters": [{"description": "调拨单物料规格IDs", "name": "request", "in": "body", "required": true, "schema": {"$ref": "#/definitions/daisy-server_api_v1.BatchDeleteParams"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/daisy-server_api_v1.Response"}}}}}, "/wms/transfer-skus/{id}": {"get": {"security": [{"Bearer": []}], "description": "获取指定ID的调拨单物料规格信息", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["仓储模块", "调拨单物料规格管理"], "summary": "获取调拨单物料规格", "parameters": [{"type": "integer", "description": "调拨单物料规格ID", "name": "id", "in": "path", "required": true}, {"type": "string", "description": "展开关联信息，支持: transfer,item,sku", "name": "_expand", "in": "query"}], "responses": {"200": {"description": "OK", "schema": {"allOf": [{"$ref": "#/definitions/daisy-server_api_v1.Response"}, {"type": "object", "properties": {"data": {"$ref": "#/definitions/daisy-server_api_v1.WmsTransferSkuResponse"}}}]}}}}, "delete": {"security": [{"Bearer": []}], "description": "删除指定ID的调拨单物料规格", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["仓储模块", "调拨单物料规格管理"], "summary": "删除调拨单物料规格", "parameters": [{"type": "integer", "description": "调拨单物料规格ID", "name": "id", "in": "path", "required": true}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/daisy-server_api_v1.Response"}}}}, "patch": {"security": [{"Bearer": []}], "description": "更新指定ID的调拨单物料规格信息", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["仓储模块", "调拨单物料规格管理"], "summary": "更新调拨单物料规格", "parameters": [{"type": "integer", "description": "调拨单物料规格ID", "name": "id", "in": "path", "required": true}, {"description": "调拨单物料规格信息", "name": "request", "in": "body", "required": true, "schema": {"$ref": "#/definitions/daisy-server_api_v1.WmsTransferSkuUpdateParams"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/daisy-server_api_v1.Response"}}}}}, "/wms/transfers": {"get": {"security": [{"Bearer": []}], "description": "分页获取调拨单列表", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["仓储模块", "调拨单管理"], "summary": "获取调拨单列表", "parameters": [{"type": "integer", "description": "当前页码(默认值: 1)", "name": "_page", "in": "query"}, {"type": "integer", "description": "每页数量(默认值: 10)", "name": "_limit", "in": "query"}, {"type": "string", "description": "排序字段，多个字段用逗号分隔", "name": "_sort", "in": "query"}, {"type": "string", "description": "排序方式，asc或desc，多个方式用逗号分隔", "name": "_order", "in": "query"}, {"type": "string", "description": "全文搜索", "name": "q", "in": "query"}, {"type": "integer", "description": "状态筛选 1草稿 2待审核 3作业中 4已完成 5已作废", "name": "status", "in": "query"}, {"type": "string", "description": "编号筛选", "name": "code", "in": "query"}, {"type": "integer", "description": "类型筛选", "name": "type", "in": "query"}], "responses": {"200": {"description": "OK", "schema": {"allOf": [{"$ref": "#/definitions/daisy-server_api_v1.Response"}, {"type": "object", "properties": {"data": {"allOf": [{"$ref": "#/definitions/daisy-server_pkg_pagination.Result"}, {"type": "object", "properties": {"records": {"type": "array", "items": {"$ref": "#/definitions/daisy-server_api_v1.WmsTransferResponse"}}}}]}}}]}}}}, "post": {"security": [{"Bearer": []}], "description": "创建新的调拨单记录", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["仓储模块", "调拨单管理"], "summary": "创建调拨单", "parameters": [{"description": "调拨单信息", "name": "request", "in": "body", "required": true, "schema": {"$ref": "#/definitions/daisy-server_api_v1.WmsTransferCreateParams"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/daisy-server_api_v1.Response"}}}}, "delete": {"security": [{"Bearer": []}], "description": "批量删除指定IDs的调拨单", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["仓储模块", "调拨单管理"], "summary": "批量删除调拨单", "parameters": [{"description": "调拨单IDs", "name": "request", "in": "body", "required": true, "schema": {"$ref": "#/definitions/daisy-server_api_v1.BatchDeleteParams"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/daisy-server_api_v1.Response"}}}}}, "/wms/transfers/{id}": {"get": {"security": [{"Bearer": []}], "description": "获取指定ID的调拨单信息", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["仓储模块", "调拨单管理"], "summary": "获取调拨单", "parameters": [{"type": "integer", "description": "调拨单ID", "name": "id", "in": "path", "required": true}], "responses": {"200": {"description": "OK", "schema": {"allOf": [{"$ref": "#/definitions/daisy-server_api_v1.Response"}, {"type": "object", "properties": {"data": {"$ref": "#/definitions/daisy-server_api_v1.WmsTransferResponse"}}}]}}}}, "delete": {"security": [{"Bearer": []}], "description": "删除指定ID的调拨单", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["仓储模块", "调拨单管理"], "summary": "删除调拨单", "parameters": [{"type": "integer", "description": "调拨单ID", "name": "id", "in": "path", "required": true}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/daisy-server_api_v1.Response"}}}}, "patch": {"security": [{"Bearer": []}], "description": "更新指定ID的调拨单信息", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["仓储模块", "调拨单管理"], "summary": "更新调拨单", "parameters": [{"type": "integer", "description": "调拨单ID", "name": "id", "in": "path", "required": true}, {"description": "调拨单信息", "name": "request", "in": "body", "required": true, "schema": {"$ref": "#/definitions/daisy-server_api_v1.WmsTransferUpdateParams"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/daisy-server_api_v1.Response"}}}}}, "/wms/units": {"get": {"security": [{"Bearer": []}], "description": "分页获取物料单位列表", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["仓储模块", "物料单位管理"], "summary": "获取物料单位列表", "parameters": [{"type": "integer", "description": "当前页码(默认值: 1)", "name": "_page", "in": "query"}, {"type": "integer", "description": "每页数量(默认值: 10)", "name": "_limit", "in": "query"}, {"type": "string", "description": "排序字段，多个字段用逗号分隔", "name": "_sort", "in": "query"}, {"type": "string", "description": "排序方式，asc或desc，多个方式用逗号分隔", "name": "_order", "in": "query"}, {"type": "string", "description": "全文搜索", "name": "q", "in": "query"}, {"type": "boolean", "description": "状态筛选", "name": "status", "in": "query"}], "responses": {"200": {"description": "OK", "schema": {"allOf": [{"$ref": "#/definitions/daisy-server_api_v1.Response"}, {"type": "object", "properties": {"data": {"allOf": [{"$ref": "#/definitions/daisy-server_pkg_pagination.Result"}, {"type": "object", "properties": {"records": {"type": "array", "items": {"$ref": "#/definitions/daisy-server_api_v1.WmsUnitResponse"}}}}]}}}]}}}}, "post": {"security": [{"Bearer": []}], "description": "创建新的物料单位记录", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["仓储模块", "物料单位管理"], "summary": "创建物料单位", "parameters": [{"description": "物料单位信息", "name": "request", "in": "body", "required": true, "schema": {"$ref": "#/definitions/daisy-server_api_v1.WmsUnitCreateParams"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/daisy-server_api_v1.Response"}}}}, "delete": {"security": [{"Bearer": []}], "description": "批量删除指定IDs的物料单位", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["仓储模块", "物料单位管理"], "summary": "批量删除物料单位", "parameters": [{"description": "物料单位IDs", "name": "request", "in": "body", "required": true, "schema": {"$ref": "#/definitions/daisy-server_api_v1.BatchDeleteParams"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/daisy-server_api_v1.Response"}}}}}, "/wms/units/{id}": {"get": {"security": [{"Bearer": []}], "description": "获取指定ID的物料单位信息", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["仓储模块", "物料单位管理"], "summary": "获取物料单位", "parameters": [{"type": "integer", "description": "物料单位ID", "name": "id", "in": "path", "required": true}], "responses": {"200": {"description": "OK", "schema": {"allOf": [{"$ref": "#/definitions/daisy-server_api_v1.Response"}, {"type": "object", "properties": {"data": {"$ref": "#/definitions/daisy-server_api_v1.WmsUnitResponse"}}}]}}}}, "delete": {"security": [{"Bearer": []}], "description": "删除指定ID的物料单位", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["仓储模块", "物料单位管理"], "summary": "删除物料单位", "parameters": [{"type": "integer", "description": "物料单位ID", "name": "id", "in": "path", "required": true}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/daisy-server_api_v1.Response"}}}}, "patch": {"security": [{"Bearer": []}], "description": "更新指定ID的物料单位信息", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["仓储模块", "物料单位管理"], "summary": "更新物料单位", "parameters": [{"type": "integer", "description": "物料单位ID", "name": "id", "in": "path", "required": true}, {"description": "物料单位信息", "name": "request", "in": "body", "required": true, "schema": {"$ref": "#/definitions/daisy-server_api_v1.WmsUnitUpdateParams"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/daisy-server_api_v1.Response"}}}}}}, "definitions": {"daisy-server_api_v1.AuthLoginData": {"type": "object", "properties": {"refreshToken": {"type": "string"}, "token": {"type": "string"}}}, "daisy-server_api_v1.AuthLoginParams": {"type": "object", "required": ["password", "tenant", "username"], "properties": {"password": {"type": "string", "example": "123456"}, "tenant": {"type": "string", "example": "tenant-code"}, "username": {"type": "string", "example": "admin"}}}, "daisy-server_api_v1.AuthLoginResponse": {"type": "object", "properties": {"code": {"type": "integer"}, "data": {"$ref": "#/definitions/daisy-server_api_v1.AuthLoginData"}, "message": {"type": "string"}}}, "daisy-server_api_v1.AuthPasswordUpdateParams": {"type": "object", "required": ["oldpass", "password", "repass"], "properties": {"oldpass": {"type": "string", "example": "123456"}, "password": {"type": "string", "example": "654321"}, "repass": {"type": "string", "example": "654321"}}}, "daisy-server_api_v1.AuthRefreshParams": {"type": "object", "required": ["refreshToken"], "properties": {"refreshToken": {"type": "string", "example": "refreshToken"}}}, "daisy-server_api_v1.AuthUserinfoResponse": {"type": "object", "properties": {"avatar": {"type": "string"}, "createdAt": {"type": "string"}, "email": {"type": "string"}, "gender": {"type": "integer"}, "id": {"type": "integer"}, "nickname": {"type": "string"}, "phone": {"type": "string"}, "roleIds": {"type": "array", "items": {"type": "integer"}}, "roles": {"description": "角色列表", "type": "array", "items": {"$ref": "#/definitions/daisy-server_api_v1.SysRoleResponse"}}, "status": {"type": "boolean"}, "tenant": {"description": "租户信息", "allOf": [{"$ref": "#/definitions/daisy-server_api_v1.SysTenantResponse"}]}, "tenantId": {"type": "integer"}, "updatedAt": {"type": "string"}, "userId": {"type": "string"}, "username": {"type": "string"}}}, "daisy-server_api_v1.AuthUserinfoUpdateParams": {"type": "object", "required": ["email"], "properties": {"avatar": {"type": "string", "example": "https://example.com/avatar.png"}, "email": {"type": "string", "example": "<EMAIL>"}, "gender": {"type": "integer", "example": 1}, "nickname": {"type": "string", "example": "alan"}, "phone": {"type": "string", "example": "***********"}}}, "daisy-server_api_v1.BatchDeleteParams": {"type": "object", "required": ["ids"], "properties": {"ids": {"type": "array", "items": {"type": "integer"}, "example": [1, 2, 3]}}}, "daisy-server_api_v1.CmsMetaCreateParams": {"type": "object", "required": ["name", "slug"], "properties": {"cover": {"type": "string", "example": "https://example.com/cover.jpg"}, "name": {"type": "string", "maxLength": 64, "example": "技术文章"}, "order": {"type": "integer", "example": 1}, "parentId": {"type": "integer", "example": 0}, "slug": {"type": "string", "maxLength": 64, "example": "tech"}, "status": {"type": "boolean", "example": true}, "summary": {"type": "string", "example": "技术相关文章"}}}, "daisy-server_api_v1.CmsMetaResponse": {"type": "object", "properties": {"cover": {"type": "string"}, "createdAt": {"type": "string"}, "id": {"type": "integer"}, "name": {"type": "string"}, "order": {"type": "integer"}, "parentId": {"type": "integer"}, "slug": {"type": "string"}, "status": {"type": "boolean"}, "summary": {"type": "string"}, "updatedAt": {"type": "string"}}}, "daisy-server_api_v1.CmsMetaUpdateParams": {"type": "object", "required": ["name", "slug"], "properties": {"cover": {"type": "string", "example": "https://example.com/cover.jpg"}, "name": {"type": "string", "maxLength": 64, "example": "技术文章"}, "order": {"type": "integer", "example": 1}, "parentId": {"type": "integer", "example": 0}, "slug": {"type": "string", "maxLength": 64, "example": "tech"}, "status": {"type": "boolean", "example": true}, "summary": {"type": "string", "example": "技术相关文章"}}}, "daisy-server_api_v1.CmsPostCreateParams": {"type": "object", "required": ["metaId", "title"], "properties": {"author": {"type": "string", "example": "作者名"}, "content": {"type": "string", "example": "文章内容"}, "cover": {"type": "string", "example": "https://example.com/cover.jpg"}, "files": {"type": "array", "items": {"$ref": "#/definitions/daisy-server_api_v1.UploadFileParams"}}, "flag": {"type": "array", "items": {"type": "integer"}, "example": [1, 2]}, "from": {"type": "string", "example": "来源"}, "metaId": {"type": "integer", "example": 1}, "order": {"type": "integer", "example": 1}, "password": {"type": "string", "example": "访问密码"}, "slug": {"type": "string", "example": "article-slug"}, "status": {"type": "boolean", "example": true}, "summary": {"type": "string", "example": "文章摘要"}, "tags": {"type": "array", "items": {"type": "string"}, "example": ["标签1", "标签2"]}, "title": {"type": "string", "maxLength": 64, "example": "文章标题"}}}, "daisy-server_api_v1.CmsPostResponse": {"type": "object", "properties": {"author": {"type": "string"}, "content": {"type": "string"}, "cover": {"type": "string"}, "createdAt": {"type": "string"}, "files": {"type": "array", "items": {"$ref": "#/definitions/daisy-server_api_v1.UploadFileParams"}}, "flag": {"type": "array", "items": {"type": "integer"}}, "from": {"type": "string"}, "id": {"type": "integer"}, "metaId": {"type": "integer"}, "order": {"type": "integer"}, "password": {"type": "string"}, "slug": {"type": "string"}, "status": {"type": "boolean"}, "summary": {"type": "string"}, "tags": {"type": "array", "items": {"type": "integer"}}, "title": {"type": "string"}, "updatedAt": {"type": "string"}}}, "daisy-server_api_v1.CmsPostUpdateParams": {"type": "object", "required": ["metaId", "title"], "properties": {"author": {"type": "string", "example": "作者名"}, "content": {"type": "string", "example": "文章内容"}, "cover": {"type": "string", "example": "https://example.com/cover.jpg"}, "files": {"type": "array", "items": {"$ref": "#/definitions/daisy-server_api_v1.UploadFileParams"}}, "flag": {"type": "array", "items": {"type": "integer"}, "example": [1, 2]}, "from": {"type": "string", "example": "来源"}, "metaId": {"type": "integer", "example": 1}, "order": {"type": "integer", "example": 1}, "password": {"type": "string", "example": "访问密码"}, "slug": {"type": "string", "example": "article-slug"}, "status": {"type": "boolean", "example": true}, "summary": {"type": "string", "example": "文章摘要"}, "tags": {"type": "array", "items": {"type": "string"}, "example": ["标签1", "标签2"]}, "title": {"type": "string", "maxLength": 64, "example": "文章标题"}}}, "daisy-server_api_v1.Response": {"type": "object", "properties": {"code": {"type": "integer"}, "data": {}, "message": {"type": "string"}}}, "daisy-server_api_v1.SysApiCreateParams": {"type": "object", "required": ["method", "path"], "properties": {"method": {"type": "string", "example": "GET"}, "path": {"type": "string", "example": "/system/user/list"}, "status": {"type": "boolean", "example": true}, "summary": {"type": "string", "example": "获取用户列表"}, "tags": {"type": "array", "items": {"type": "string"}, "example": ["标签1", "标签2"]}}}, "daisy-server_api_v1.SysApiResponse": {"type": "object", "properties": {"createdAt": {"type": "string"}, "id": {"type": "integer"}, "method": {"type": "string"}, "path": {"type": "string"}, "status": {"type": "boolean"}, "summary": {"type": "string"}, "tags": {"type": "array", "items": {"type": "integer"}}, "updatedAt": {"type": "string"}}}, "daisy-server_api_v1.SysApiUpdateParams": {"type": "object", "required": ["method", "path"], "properties": {"method": {"type": "string", "example": "GET"}, "path": {"type": "string", "example": "/system/user/list"}, "status": {"type": "boolean", "example": true}, "summary": {"type": "string", "example": "获取用户列表"}, "tags": {"type": "array", "items": {"type": "string"}, "example": ["标签1", "标签2"]}}}, "daisy-server_api_v1.SysConfigCreateParams": {"type": "object", "required": ["code", "name"], "properties": {"code": {"type": "string", "maxLength": 64, "example": "system"}, "name": {"type": "string", "maxLength": 64, "example": "系统配置"}, "params": {"type": "array", "items": {"$ref": "#/definitions/daisy-server_api_v1.SysConfigParam"}}, "status": {"type": "boolean", "example": true}, "summary": {"type": "string", "example": "系统基础配置"}}}, "daisy-server_api_v1.SysConfigParam": {"type": "object", "properties": {"code": {"type": "string"}, "id": {"type": "string"}, "name": {"type": "string"}, "status": {"type": "boolean"}, "summary": {"type": "string"}, "value": {"type": "string"}}}, "daisy-server_api_v1.SysConfigResponse": {"type": "object", "properties": {"code": {"type": "string"}, "createdAt": {"type": "string"}, "id": {"type": "integer"}, "name": {"type": "string"}, "params": {"type": "array", "items": {"$ref": "#/definitions/daisy-server_api_v1.SysConfigParam"}}, "status": {"type": "boolean"}, "summary": {"type": "string"}, "updatedAt": {"type": "string"}}}, "daisy-server_api_v1.SysConfigUpdateParams": {"type": "object", "required": ["code", "name"], "properties": {"code": {"type": "string", "maxLength": 64, "example": "system"}, "name": {"type": "string", "maxLength": 64, "example": "系统配置"}, "params": {"type": "array", "items": {"$ref": "#/definitions/daisy-server_api_v1.SysConfigParam"}}, "status": {"type": "boolean", "example": true}, "summary": {"type": "string", "example": "系统基础配置"}}}, "daisy-server_api_v1.SysDictCreateParams": {"type": "object", "required": ["code", "name"], "properties": {"code": {"type": "string", "maxLength": 64, "example": "gender"}, "name": {"type": "string", "maxLength": 64, "example": "性别"}, "options": {"type": "array", "items": {"$ref": "#/definitions/daisy-server_api_v1.SysDictOption"}}, "status": {"type": "boolean", "example": true}, "summary": {"type": "string", "example": "性别字典"}}}, "daisy-server_api_v1.SysDictOption": {"type": "object", "properties": {"id": {"type": "string"}, "label": {"type": "string"}, "status": {"type": "boolean"}, "type": {"type": "string"}, "value": {"type": "string"}}}, "daisy-server_api_v1.SysDictResponse": {"type": "object", "properties": {"code": {"type": "string"}, "createdAt": {"type": "string"}, "id": {"type": "integer"}, "name": {"type": "string"}, "options": {"type": "array", "items": {"$ref": "#/definitions/daisy-server_api_v1.SysDictOption"}}, "status": {"type": "boolean"}, "summary": {"type": "string"}, "updatedAt": {"type": "string"}}}, "daisy-server_api_v1.SysDictUpdateParams": {"type": "object", "required": ["code", "name"], "properties": {"code": {"type": "string", "maxLength": 64, "example": "gender"}, "name": {"type": "string", "maxLength": 64, "example": "性别"}, "options": {"type": "array", "items": {"$ref": "#/definitions/daisy-server_api_v1.SysDictOption"}}, "status": {"type": "boolean", "example": true}, "summary": {"type": "string", "example": "性别字典"}}}, "daisy-server_api_v1.SysLogResponse": {"type": "object", "properties": {"clientIp": {"type": "string"}, "code": {"type": "integer"}, "createdAt": {"type": "string"}, "id": {"type": "integer"}, "method": {"type": "string"}, "params": {"type": "array", "items": {"type": "integer"}}, "path": {"type": "string"}, "time": {"type": "number"}, "updatedAt": {"type": "string"}, "userAgent": {"type": "string"}, "userId": {"type": "string"}}}, "daisy-server_api_v1.SysMenuCreateParams": {"type": "object", "required": ["layout", "menuName", "menuType", "routeName", "routePath"], "properties": {"component": {"type": "string", "example": "UserList"}, "constant": {"type": "boolean", "example": false}, "hideInMenu": {"type": "boolean", "example": false}, "href": {"type": "string", "example": "https://example.com"}, "icon": {"type": "string", "example": "user"}, "keepAlive": {"type": "boolean", "example": false}, "layout": {"type": "string", "example": "basic"}, "menuName": {"type": "string", "maxLength": 64, "example": "用户管理"}, "menuType": {"type": "integer", "example": 0}, "multiTab": {"type": "boolean", "example": false}, "order": {"type": "integer", "example": 1}, "parentId": {"type": "integer", "example": 0}, "routeName": {"type": "string", "maxLength": 64, "example": "user"}, "routePath": {"type": "string", "example": "/user"}, "status": {"type": "boolean", "example": true}}}, "daisy-server_api_v1.SysMenuResponse": {"type": "object", "properties": {"component": {"type": "string"}, "constant": {"type": "boolean"}, "createdAt": {"type": "string"}, "hideInMenu": {"type": "boolean"}, "href": {"type": "string"}, "icon": {"type": "string"}, "id": {"type": "integer"}, "keepAlive": {"type": "boolean"}, "layout": {"type": "string"}, "menuName": {"type": "string"}, "menuType": {"type": "integer"}, "multiTab": {"type": "boolean"}, "order": {"type": "integer"}, "parentId": {"type": "integer"}, "routeName": {"type": "string"}, "routePath": {"type": "string"}, "status": {"type": "boolean"}, "updatedAt": {"type": "string"}}}, "daisy-server_api_v1.SysMenuUpdateParams": {"type": "object", "required": ["layout", "menuName", "menuType", "routeName", "routePath"], "properties": {"component": {"type": "string", "example": "UserList"}, "constant": {"type": "boolean", "example": false}, "hideInMenu": {"type": "boolean", "example": false}, "href": {"type": "string", "example": "https://example.com"}, "icon": {"type": "string", "example": "user"}, "keepAlive": {"type": "boolean", "example": false}, "layout": {"type": "string", "example": "basic"}, "menuName": {"type": "string", "maxLength": 64, "example": "用户管理"}, "menuType": {"type": "integer", "example": 0}, "multiTab": {"type": "boolean", "example": false}, "order": {"type": "integer", "example": 1}, "parentId": {"type": "integer", "example": 0}, "routeName": {"type": "string", "maxLength": 64, "example": "user"}, "routePath": {"type": "string", "example": "/user"}, "status": {"type": "boolean", "example": true}}}, "daisy-server_api_v1.SysRoleCreateParams": {"type": "object", "required": ["code", "name"], "properties": {"code": {"type": "string", "maxLength": 64, "example": "admin"}, "name": {"type": "string", "maxLength": 64, "example": "管理员"}, "status": {"type": "boolean", "example": true}, "summary": {"type": "string", "example": "系统管理员角色"}}}, "daisy-server_api_v1.SysRolePermitParams": {"type": "object", "properties": {"apiIds": {"type": "array", "items": {"type": "integer"}, "example": [1, 2, 3]}, "home": {"type": "string", "example": "home"}, "menuIds": {"type": "array", "items": {"type": "integer"}, "example": [1, 2, 3]}}}, "daisy-server_api_v1.SysRoleResponse": {"type": "object", "properties": {"apiIds": {"type": "array", "items": {"type": "integer"}}, "code": {"type": "string"}, "createdAt": {"type": "string"}, "home": {"type": "string"}, "id": {"type": "integer"}, "menuIds": {"type": "array", "items": {"type": "integer"}}, "name": {"type": "string"}, "status": {"type": "boolean"}, "summary": {"type": "string"}, "updatedAt": {"type": "string"}}}, "daisy-server_api_v1.SysRoleUpdateParams": {"type": "object", "required": ["code", "name"], "properties": {"code": {"type": "string", "maxLength": 64, "example": "admin"}, "name": {"type": "string", "maxLength": 64, "example": "管理员"}, "status": {"type": "boolean", "example": true}, "summary": {"type": "string", "example": "系统管理员角色"}}}, "daisy-server_api_v1.SysTenantCreateParams": {"type": "object", "required": ["code", "name"], "properties": {"code": {"type": "string", "maxLength": 64, "example": "tenant-code"}, "expiredAt": {"type": "string", "example": "2024-12-31T23:59:59Z"}, "name": {"type": "string", "maxLength": 64, "example": "租户名称"}, "status": {"type": "boolean", "example": true}, "summary": {"type": "string", "example": "租户描述"}}}, "daisy-server_api_v1.SysTenantResponse": {"type": "object", "properties": {"code": {"type": "string"}, "createdAt": {"type": "string"}, "expiredAt": {"type": "string"}, "id": {"type": "integer"}, "name": {"type": "string"}, "status": {"type": "boolean"}, "summary": {"type": "string"}, "updatedAt": {"type": "string"}}}, "daisy-server_api_v1.SysTenantUpdateParams": {"type": "object", "required": ["code", "name"], "properties": {"code": {"type": "string", "maxLength": 64, "example": "tenant-code"}, "expiredAt": {"type": "string", "example": "2024-12-31T23:59:59Z"}, "name": {"type": "string", "maxLength": 64, "example": "租户名称"}, "status": {"type": "boolean", "example": true}, "summary": {"type": "string", "example": "租户描述"}}}, "daisy-server_api_v1.SysUserCreateParams": {"type": "object", "required": ["username"], "properties": {"avatar": {"type": "string", "example": "https://example.com/avatar.png"}, "email": {"type": "string", "example": "<EMAIL>"}, "gender": {"type": "integer", "example": 0}, "nickname": {"type": "string", "maxLength": 64, "example": "管理员"}, "password": {"type": "string", "example": "123456"}, "phone": {"type": "string", "example": "***********"}, "roleIds": {"type": "array", "items": {"type": "integer"}, "example": [1, 2, 3]}, "status": {"type": "boolean", "example": true}, "tenantId": {"type": "integer", "example": 1}, "username": {"type": "string", "maxLength": 64, "example": "admin"}}}, "daisy-server_api_v1.SysUserResponse": {"type": "object", "properties": {"avatar": {"type": "string"}, "createdAt": {"type": "string"}, "email": {"type": "string"}, "gender": {"type": "integer"}, "id": {"type": "integer"}, "nickname": {"type": "string"}, "phone": {"type": "string"}, "roleIds": {"type": "array", "items": {"type": "integer"}}, "status": {"type": "boolean"}, "tenant": {"description": "租户信息", "allOf": [{"$ref": "#/definitions/daisy-server_api_v1.SysTenantResponse"}]}, "tenantId": {"type": "integer"}, "updatedAt": {"type": "string"}, "userId": {"type": "string"}, "username": {"type": "string"}}}, "daisy-server_api_v1.SysUserUpdateParams": {"type": "object", "required": ["username"], "properties": {"avatar": {"type": "string", "example": "https://example.com/avatar.png"}, "email": {"type": "string", "example": "<EMAIL>"}, "gender": {"type": "integer", "example": 0}, "nickname": {"type": "string", "maxLength": 64, "example": "管理员"}, "password": {"type": "string", "example": "123456"}, "phone": {"type": "string", "example": "***********"}, "roleIds": {"type": "array", "items": {"type": "integer"}, "example": [1, 2, 3]}, "status": {"type": "boolean", "example": true}, "tenantId": {"type": "integer", "example": 1}, "username": {"type": "string", "maxLength": 64, "example": "admin"}}}, "daisy-server_api_v1.UploadFileParams": {"type": "object", "properties": {"id": {"type": "string", "example": "123456"}, "name": {"type": "string", "example": "文件名称"}, "size": {"type": "integer", "example": 1024}, "type": {"type": "string", "example": "application/pdf"}, "url": {"type": "string", "example": "https://example.com/file.pdf"}}}, "daisy-server_api_v1.UploadRemoveParams": {"type": "object", "required": ["fileName"], "properties": {"fileName": {"type": "string", "example": "images/123456.jpg"}}}, "daisy-server_api_v1.WmsAreaCreateParams": {"type": "object", "required": ["code", "name", "type"], "properties": {"code": {"type": "string", "maxLength": 64, "example": "WH001"}, "createdBy": {"type": "string", "example": "管理员"}, "name": {"type": "string", "maxLength": 64, "example": "仓库A"}, "order": {"type": "integer", "example": 1}, "parentId": {"type": "integer", "example": 0}, "parentPath": {"type": "array", "items": {"type": "integer"}, "example": [1, 2, 3]}, "status": {"type": "boolean", "example": true}, "summary": {"type": "string", "example": "主仓库"}, "tenantId": {"type": "integer", "example": 1}, "type": {"type": "integer", "example": 1}, "updatedBy": {"type": "string", "example": "管理员"}}}, "daisy-server_api_v1.WmsAreaResponse": {"type": "object", "properties": {"code": {"type": "string"}, "createdAt": {"type": "string"}, "createdBy": {"type": "string"}, "id": {"type": "integer"}, "name": {"type": "string"}, "order": {"type": "integer"}, "parentId": {"type": "integer"}, "parentPath": {"type": "array", "items": {"type": "integer"}}, "status": {"type": "boolean"}, "summary": {"type": "string"}, "tenantId": {"type": "integer"}, "type": {"type": "integer"}, "updatedAt": {"type": "string"}, "updatedBy": {"type": "string"}}}, "daisy-server_api_v1.WmsAreaUpdateParams": {"type": "object", "required": ["code", "name", "type"], "properties": {"code": {"type": "string", "maxLength": 64, "example": "WH001"}, "createdBy": {"type": "string", "example": "管理员"}, "name": {"type": "string", "maxLength": 64, "example": "仓库A"}, "order": {"type": "integer", "example": 1}, "parentId": {"type": "integer", "example": 0}, "parentPath": {"type": "array", "items": {"type": "integer"}, "example": [1, 2, 3]}, "status": {"type": "boolean", "example": true}, "summary": {"type": "string", "example": "主仓库"}, "tenantId": {"type": "integer", "example": 1}, "type": {"type": "integer", "example": 1}, "updatedBy": {"type": "string", "example": "管理员"}}}, "daisy-server_api_v1.WmsCheckCreateParams": {"type": "object", "required": ["areaPath", "code", "type"], "properties": {"areaPath": {"type": "array", "items": {"type": "integer"}, "example": [1, 2, 3]}, "code": {"type": "string", "maxLength": 64, "example": "CHK202312010001"}, "createdBy": {"type": "string", "example": "管理员"}, "status": {"type": "integer", "example": 1}, "summary": {"type": "string", "example": "库存盘点"}, "tenantId": {"type": "integer", "example": 1}, "type": {"type": "integer", "example": 1}, "updatedBy": {"type": "string", "example": "管理员"}}}, "daisy-server_api_v1.WmsCheckResponse": {"type": "object", "properties": {"areaPath": {"type": "array", "items": {"type": "integer"}}, "code": {"type": "string"}, "createdAt": {"type": "string"}, "createdBy": {"type": "string"}, "id": {"type": "integer"}, "status": {"type": "integer"}, "summary": {"type": "string"}, "tenantId": {"type": "integer"}, "type": {"type": "integer"}, "updatedAt": {"type": "string"}, "updatedBy": {"type": "string"}}}, "daisy-server_api_v1.WmsCheckSkuCreateParams": {"type": "object", "required": ["checkId", "itemId", "skuId"], "properties": {"checkId": {"type": "integer", "example": 1}, "createdBy": {"type": "string", "example": "管理员"}, "itemId": {"type": "integer", "example": 1}, "num": {"type": "number", "minimum": 0, "example": 100.5}, "remain": {"type": "number", "minimum": 0, "example": 95.3}, "skuId": {"type": "integer", "example": 1}, "status": {"type": "boolean", "example": false}, "tenantId": {"type": "integer", "example": 1}, "updatedBy": {"type": "string", "example": "管理员"}}}, "daisy-server_api_v1.WmsCheckSkuResponse": {"type": "object", "properties": {"check": {"description": "盘点单信息", "allOf": [{"$ref": "#/definitions/daisy-server_api_v1.WmsCheckResponse"}]}, "checkId": {"type": "integer"}, "createdAt": {"type": "string"}, "createdBy": {"type": "string"}, "id": {"type": "integer"}, "item": {"description": "物料信息", "allOf": [{"$ref": "#/definitions/daisy-server_api_v1.WmsItemResponse"}]}, "itemId": {"type": "integer"}, "num": {"type": "number"}, "remain": {"type": "number"}, "sku": {"description": "规格信息", "allOf": [{"$ref": "#/definitions/daisy-server_api_v1.WmsSkuResponse"}]}, "skuId": {"type": "integer"}, "status": {"type": "boolean"}, "tenantId": {"type": "integer"}, "updatedAt": {"type": "string"}, "updatedBy": {"type": "string"}}}, "daisy-server_api_v1.WmsCheckSkuUpdateParams": {"type": "object", "required": ["checkId", "itemId", "skuId"], "properties": {"checkId": {"type": "integer", "example": 1}, "createdBy": {"type": "string", "example": "管理员"}, "itemId": {"type": "integer", "example": 1}, "num": {"type": "number", "minimum": 0, "example": 100.5}, "remain": {"type": "number", "minimum": 0, "example": 95.3}, "skuId": {"type": "integer", "example": 1}, "status": {"type": "boolean", "example": false}, "tenantId": {"type": "integer", "example": 1}, "updatedBy": {"type": "string", "example": "管理员"}}}, "daisy-server_api_v1.WmsCheckUpdateParams": {"type": "object", "required": ["areaPath", "code", "type"], "properties": {"areaPath": {"type": "array", "items": {"type": "integer"}, "example": [1, 2, 3]}, "code": {"type": "string", "maxLength": 64, "example": "CHK202312010001"}, "createdBy": {"type": "string", "example": "管理员"}, "status": {"type": "integer", "example": 1}, "summary": {"type": "string", "example": "库存盘点"}, "tenantId": {"type": "integer", "example": 1}, "type": {"type": "integer", "example": 1}, "updatedBy": {"type": "string", "example": "管理员"}}}, "daisy-server_api_v1.WmsDeliverCreateParams": {"type": "object", "required": ["code", "type"], "properties": {"code": {"type": "string", "maxLength": 64, "example": "DEL202312010001"}, "createdBy": {"type": "string", "example": "管理员"}, "partnerId": {"type": "integer", "example": 1}, "relatedNo": {"type": "string", "maxLength": 64, "example": "SO202312010001"}, "status": {"type": "integer", "example": 1}, "summary": {"type": "string", "example": "销售出库"}, "tenantId": {"type": "integer", "example": 1}, "type": {"type": "integer", "example": 1}, "updatedBy": {"type": "string", "example": "管理员"}}}, "daisy-server_api_v1.WmsDeliverResponse": {"type": "object", "properties": {"code": {"type": "string"}, "createdAt": {"type": "string"}, "createdBy": {"type": "string"}, "id": {"type": "integer"}, "partner": {"description": "客户信息", "allOf": [{"$ref": "#/definitions/daisy-server_api_v1.WmsPartnerResponse"}]}, "partnerId": {"type": "integer"}, "relatedNo": {"type": "string"}, "status": {"type": "integer"}, "summary": {"type": "string"}, "tenantId": {"type": "integer"}, "type": {"type": "integer"}, "updatedAt": {"type": "string"}, "updatedBy": {"type": "string"}}}, "daisy-server_api_v1.WmsDeliverSkuCreateParams": {"type": "object"}, "daisy-server_api_v1.WmsDeliverSkuResponse": {"type": "object", "properties": {"areaNum": {"type": "array", "items": {"type": "integer"}}, "createdAt": {"type": "string"}, "createdBy": {"type": "string"}, "deliver": {"description": "出库单信息", "allOf": [{"$ref": "#/definitions/daisy-server_api_v1.WmsDeliverResponse"}]}, "deliverId": {"type": "integer"}, "id": {"type": "integer"}, "item": {"description": "物料信息", "allOf": [{"$ref": "#/definitions/daisy-server_api_v1.WmsItemResponse"}]}, "itemId": {"type": "integer"}, "num": {"type": "number"}, "pcs": {"type": "number"}, "sku": {"description": "规格信息", "allOf": [{"$ref": "#/definitions/daisy-server_api_v1.WmsSkuResponse"}]}, "skuId": {"type": "integer"}, "status": {"type": "boolean"}, "tenantId": {"type": "integer"}, "updatedAt": {"type": "string"}, "updatedBy": {"type": "string"}}}, "daisy-server_api_v1.WmsDeliverSkuUpdateParams": {"type": "object"}, "daisy-server_api_v1.WmsDeliverUpdateParams": {"type": "object", "required": ["code", "type"], "properties": {"code": {"type": "string", "maxLength": 64, "example": "DEL202312010001"}, "createdBy": {"type": "string", "example": "管理员"}, "partnerId": {"type": "integer", "example": 1}, "relatedNo": {"type": "string", "maxLength": 64, "example": "SO202312010001"}, "status": {"type": "integer", "example": 1}, "summary": {"type": "string", "example": "销售出库"}, "tenantId": {"type": "integer", "example": 1}, "type": {"type": "integer", "example": 1}, "updatedBy": {"type": "string", "example": "管理员"}}}, "daisy-server_api_v1.WmsItemCreateParams": {"type": "object", "required": ["code", "name"], "properties": {"code": {"type": "string", "maxLength": 64, "example": "IP15001"}, "createdBy": {"type": "string", "example": "管理员"}, "metaId": {"type": "integer", "example": 1}, "name": {"type": "string", "maxLength": 255, "example": "iPhone 15"}, "order": {"type": "integer", "example": 1}, "status": {"type": "boolean", "example": true}, "summary": {"type": "string", "example": "苹果手机 iPhone 15 128GB"}, "tenantId": {"type": "integer", "example": 1}, "updatedBy": {"type": "string", "example": "管理员"}}}, "daisy-server_api_v1.WmsItemResponse": {"type": "object", "properties": {"code": {"type": "string"}, "createdAt": {"type": "string"}, "createdBy": {"type": "string"}, "id": {"type": "integer"}, "metaId": {"type": "integer"}, "name": {"type": "string"}, "order": {"type": "integer"}, "status": {"type": "boolean"}, "summary": {"type": "string"}, "tenantId": {"type": "integer"}, "updatedAt": {"type": "string"}, "updatedBy": {"type": "string"}}}, "daisy-server_api_v1.WmsItemUpdateParams": {"type": "object", "required": ["code", "name"], "properties": {"code": {"type": "string", "maxLength": 64, "example": "IP15001"}, "createdBy": {"type": "string", "example": "管理员"}, "metaId": {"type": "integer", "example": 1}, "name": {"type": "string", "maxLength": 255, "example": "iPhone 15"}, "order": {"type": "integer", "example": 1}, "status": {"type": "boolean", "example": true}, "summary": {"type": "string", "example": "苹果手机 iPhone 15 128GB"}, "tenantId": {"type": "integer", "example": 1}, "updatedBy": {"type": "string", "example": "管理员"}}}, "daisy-server_api_v1.WmsLogCreateParams": {"type": "object", "required": ["itemId", "num", "relatedNo", "remain", "skuId", "type"], "properties": {"createdBy": {"type": "string", "example": "管理员"}, "itemId": {"type": "integer", "example": 1}, "num": {"type": "number", "example": 100}, "relatedNo": {"type": "string", "maxLength": 64, "example": "IN202312010001"}, "remain": {"type": "number", "example": 100}, "skuId": {"type": "integer", "example": 1}, "summary": {"type": "string", "example": "入库操作"}, "tenantId": {"type": "integer", "example": 1}, "type": {"type": "integer", "example": 1}, "updatedBy": {"type": "string", "example": "管理员"}}}, "daisy-server_api_v1.WmsLogResponse": {"type": "object", "properties": {"createdAt": {"type": "string"}, "createdBy": {"type": "string"}, "id": {"type": "integer"}, "item": {"description": "物料信息", "allOf": [{"$ref": "#/definitions/daisy-server_api_v1.WmsItemResponse"}]}, "itemId": {"type": "integer"}, "num": {"type": "number"}, "relatedNo": {"type": "string"}, "remain": {"type": "number"}, "sku": {"description": "SKU信息", "allOf": [{"$ref": "#/definitions/daisy-server_api_v1.WmsSkuResponse"}]}, "skuId": {"type": "integer"}, "summary": {"type": "string"}, "tenantId": {"type": "integer"}, "type": {"type": "integer"}, "updatedAt": {"type": "string"}, "updatedBy": {"type": "string"}}}, "daisy-server_api_v1.WmsLogUpdateParams": {"type": "object", "required": ["itemId", "num", "relatedNo", "remain", "skuId", "type"], "properties": {"createdBy": {"type": "string", "example": "管理员"}, "itemId": {"type": "integer", "example": 1}, "num": {"type": "number", "example": 100}, "relatedNo": {"type": "string", "maxLength": 64, "example": "IN202312010001"}, "remain": {"type": "number", "example": 100}, "skuId": {"type": "integer", "example": 1}, "summary": {"type": "string", "example": "入库操作"}, "tenantId": {"type": "integer", "example": 1}, "type": {"type": "integer", "example": 1}, "updatedBy": {"type": "string", "example": "管理员"}}}, "daisy-server_api_v1.WmsMetaCreateParams": {"type": "object", "required": ["name"], "properties": {"createdBy": {"type": "string", "example": "管理员"}, "name": {"type": "string", "maxLength": 64, "example": "电子产品"}, "order": {"type": "integer", "example": 1}, "parentId": {"type": "integer", "example": 0}, "parentPath": {"type": "array", "items": {"type": "integer"}, "example": [1, 2, 3]}, "status": {"type": "boolean", "example": true}, "tenantId": {"type": "integer", "example": 1}, "updatedBy": {"type": "string", "example": "管理员"}}}, "daisy-server_api_v1.WmsMetaResponse": {"type": "object", "properties": {"createdAt": {"type": "string"}, "createdBy": {"type": "string"}, "id": {"type": "integer"}, "name": {"type": "string"}, "order": {"type": "integer"}, "parentId": {"type": "integer"}, "parentPath": {"type": "array", "items": {"type": "integer"}}, "status": {"type": "boolean"}, "tenantId": {"type": "integer"}, "updatedAt": {"type": "string"}, "updatedBy": {"type": "string"}}}, "daisy-server_api_v1.WmsMetaUpdateParams": {"type": "object", "required": ["name"], "properties": {"createdBy": {"type": "string", "example": "管理员"}, "name": {"type": "string", "maxLength": 64, "example": "电子产品"}, "order": {"type": "integer", "example": 1}, "parentId": {"type": "integer", "example": 0}, "parentPath": {"type": "array", "items": {"type": "integer"}, "example": [1, 2, 3]}, "status": {"type": "boolean", "example": true}, "tenantId": {"type": "integer", "example": 1}, "updatedBy": {"type": "string", "example": "管理员"}}}, "daisy-server_api_v1.WmsPackCreateParams": {"type": "object", "required": ["code", "deliverId", "itemId", "num", "skuId"], "properties": {"code": {"type": "string", "maxLength": 64, "example": "PACK202312010001"}, "createdBy": {"type": "string", "example": "管理员"}, "deliverId": {"type": "integer", "example": 1}, "itemId": {"type": "integer", "example": 1}, "num": {"type": "number", "minimum": 0, "example": 100.5}, "skuId": {"type": "integer", "example": 1}, "tenantId": {"type": "integer", "example": 1}, "updatedBy": {"type": "string", "example": "管理员"}}}, "daisy-server_api_v1.WmsPackResponse": {"type": "object", "properties": {"code": {"type": "string"}, "createdAt": {"type": "string"}, "createdBy": {"type": "string"}, "deliver": {"description": "出库单信息", "allOf": [{"$ref": "#/definitions/daisy-server_api_v1.WmsDeliverResponse"}]}, "deliverId": {"type": "integer"}, "id": {"type": "integer"}, "item": {"description": "物料信息", "allOf": [{"$ref": "#/definitions/daisy-server_api_v1.WmsItemResponse"}]}, "itemId": {"type": "integer"}, "num": {"type": "number"}, "sku": {"description": "规格信息", "allOf": [{"$ref": "#/definitions/daisy-server_api_v1.WmsSkuResponse"}]}, "skuId": {"type": "integer"}, "tenantId": {"type": "integer"}, "updatedAt": {"type": "string"}, "updatedBy": {"type": "string"}}}, "daisy-server_api_v1.WmsPackUpdateParams": {"type": "object", "required": ["code", "deliverId", "itemId", "num", "skuId"], "properties": {"code": {"type": "string", "maxLength": 64, "example": "PACK202312010001"}, "createdBy": {"type": "string", "example": "管理员"}, "deliverId": {"type": "integer", "example": 1}, "itemId": {"type": "integer", "example": 1}, "num": {"type": "number", "minimum": 0, "example": 100.5}, "skuId": {"type": "integer", "example": 1}, "tenantId": {"type": "integer", "example": 1}, "updatedBy": {"type": "string", "example": "管理员"}}}, "daisy-server_api_v1.WmsPartnerCreateParams": {"type": "object", "required": ["name", "type"], "properties": {"address": {"type": "string", "example": "杭州市余杭区文一西路969号"}, "area": {"type": "string", "example": "浙江省杭州市"}, "bankAccount": {"type": "string", "example": "1234567890123456789"}, "bankName": {"type": "string", "example": "中国工商银行"}, "contact": {"type": "string", "example": "张三"}, "createdBy": {"type": "string", "example": "管理员"}, "email": {"type": "string", "example": "<EMAIL>"}, "level": {"type": "integer", "example": 1}, "name": {"type": "string", "maxLength": 64, "example": "阿里巴巴集团"}, "order": {"type": "integer", "example": 1}, "phone": {"type": "string", "example": "***********"}, "status": {"type": "boolean", "example": true}, "summary": {"type": "string", "example": "知名互联网公司"}, "tenantId": {"type": "integer", "example": 1}, "type": {"type": "array", "items": {"type": "integer"}, "example": [1, 2]}, "updatedBy": {"type": "string", "example": "管理员"}}}, "daisy-server_api_v1.WmsPartnerResponse": {"type": "object", "properties": {"address": {"type": "string"}, "area": {"type": "string"}, "bankAccount": {"type": "string"}, "bankName": {"type": "string"}, "contact": {"type": "string"}, "createdAt": {"type": "string"}, "createdBy": {"type": "string"}, "email": {"type": "string"}, "id": {"type": "integer"}, "level": {"type": "integer"}, "name": {"type": "string"}, "order": {"type": "integer"}, "phone": {"type": "string"}, "status": {"type": "boolean"}, "summary": {"type": "string"}, "tenantId": {"type": "integer"}, "type": {"type": "array", "items": {"type": "integer"}}, "updatedAt": {"type": "string"}, "updatedBy": {"type": "string"}}}, "daisy-server_api_v1.WmsPartnerUpdateParams": {"type": "object", "required": ["name", "type"], "properties": {"address": {"type": "string", "example": "杭州市余杭区文一西路969号"}, "area": {"type": "string", "example": "浙江省杭州市"}, "bankAccount": {"type": "string", "example": "1234567890123456789"}, "bankName": {"type": "string", "example": "中国工商银行"}, "contact": {"type": "string", "example": "张三"}, "createdBy": {"type": "string", "example": "管理员"}, "email": {"type": "string", "example": "<EMAIL>"}, "level": {"type": "integer", "example": 1}, "name": {"type": "string", "maxLength": 64, "example": "阿里巴巴集团"}, "order": {"type": "integer", "example": 1}, "phone": {"type": "string", "example": "***********"}, "status": {"type": "boolean", "example": true}, "summary": {"type": "string", "example": "知名互联网公司"}, "tenantId": {"type": "integer", "example": 1}, "type": {"type": "array", "items": {"type": "integer"}, "example": [1, 2]}, "updatedBy": {"type": "string", "example": "管理员"}}}, "daisy-server_api_v1.WmsReceiveCreateParams": {"type": "object", "required": ["code", "type"], "properties": {"code": {"type": "string", "maxLength": 64, "example": "REC202312010001"}, "createdBy": {"type": "string", "example": "管理员"}, "partnerId": {"type": "integer", "example": 1}, "relatedNo": {"type": "string", "maxLength": 64, "example": "PO202312010001"}, "status": {"type": "integer", "example": 1}, "summary": {"type": "string", "example": "采购入库"}, "tenantId": {"type": "integer", "example": 1}, "type": {"type": "integer", "example": 1}, "updatedBy": {"type": "string", "example": "管理员"}}}, "daisy-server_api_v1.WmsReceiveResponse": {"type": "object", "properties": {"code": {"type": "string"}, "createdAt": {"type": "string"}, "createdBy": {"type": "string"}, "id": {"type": "integer"}, "partner": {"description": "供应商信息", "allOf": [{"$ref": "#/definitions/daisy-server_api_v1.WmsPartnerResponse"}]}, "partnerId": {"type": "integer"}, "relatedNo": {"type": "string"}, "status": {"type": "integer"}, "summary": {"type": "string"}, "tenantId": {"type": "integer"}, "type": {"type": "integer"}, "updatedAt": {"type": "string"}, "updatedBy": {"type": "string"}}}, "daisy-server_api_v1.WmsReceiveSkuCreateParams": {"type": "object"}, "daisy-server_api_v1.WmsReceiveSkuResponse": {"type": "object", "properties": {"areaNum": {"type": "array", "items": {"type": "integer"}}, "batchNo": {"type": "string"}, "createdAt": {"type": "string"}, "createdBy": {"type": "string"}, "id": {"type": "integer"}, "item": {"description": "物料信息", "allOf": [{"$ref": "#/definitions/daisy-server_api_v1.WmsItemResponse"}]}, "itemId": {"type": "integer"}, "num": {"type": "number"}, "receive": {"description": "入库单信息", "allOf": [{"$ref": "#/definitions/daisy-server_api_v1.WmsReceiveResponse"}]}, "receiveId": {"type": "integer"}, "sku": {"description": "规格信息", "allOf": [{"$ref": "#/definitions/daisy-server_api_v1.WmsSkuResponse"}]}, "skuId": {"type": "integer"}, "status": {"type": "boolean"}, "tenantId": {"type": "integer"}, "updatedAt": {"type": "string"}, "updatedBy": {"type": "string"}}}, "daisy-server_api_v1.WmsReceiveSkuUpdateParams": {"type": "object"}, "daisy-server_api_v1.WmsReceiveUpdateParams": {"type": "object", "required": ["code", "type"], "properties": {"code": {"type": "string", "maxLength": 64, "example": "REC202312010001"}, "createdBy": {"type": "string", "example": "管理员"}, "partnerId": {"type": "integer", "example": 1}, "relatedNo": {"type": "string", "maxLength": 64, "example": "PO202312010001"}, "status": {"type": "integer", "example": 1}, "summary": {"type": "string", "example": "采购入库"}, "tenantId": {"type": "integer", "example": 1}, "type": {"type": "integer", "example": 1}, "updatedBy": {"type": "string", "example": "管理员"}}}, "daisy-server_api_v1.WmsSkuCreateParams": {"type": "object"}, "daisy-server_api_v1.WmsSkuResponse": {"type": "object", "properties": {"attrs": {"type": "array", "items": {"type": "integer"}}, "code": {"type": "string"}, "createdAt": {"type": "string"}, "createdBy": {"type": "string"}, "id": {"type": "integer"}, "itemId": {"type": "integer"}, "min": {"type": "number"}, "name": {"type": "string"}, "order": {"type": "integer"}, "status": {"type": "boolean"}, "summary": {"type": "string"}, "tenantId": {"type": "integer"}, "unit": {"type": "string"}, "updatedAt": {"type": "string"}, "updatedBy": {"type": "string"}}}, "daisy-server_api_v1.WmsSkuUpdateParams": {"type": "object"}, "daisy-server_api_v1.WmsStaffCreateParams": {"type": "object", "required": ["username"], "properties": {"avatar": {"type": "string", "example": "https://example.com/avatar.png"}, "email": {"type": "string", "example": "<EMAIL>"}, "gender": {"type": "integer", "example": 0}, "nickname": {"type": "string", "maxLength": 64, "example": "管理员"}, "password": {"type": "string", "example": "123456"}, "phone": {"type": "string", "example": "***********"}, "roleIds": {"type": "array", "items": {"type": "integer"}, "example": [1, 2, 3]}, "status": {"type": "boolean", "example": true}, "tenantId": {"type": "integer", "example": 1}, "username": {"type": "string", "maxLength": 64, "example": "admin"}}}, "daisy-server_api_v1.WmsStaffResponse": {"type": "object", "properties": {"avatar": {"type": "string"}, "createdAt": {"type": "string"}, "email": {"type": "string"}, "gender": {"type": "integer"}, "id": {"type": "integer"}, "nickname": {"type": "string"}, "phone": {"type": "string"}, "roleIds": {"type": "array", "items": {"type": "integer"}}, "status": {"type": "boolean"}, "tenant": {"description": "租户信息", "allOf": [{"$ref": "#/definitions/daisy-server_api_v1.SysTenantResponse"}]}, "tenantId": {"type": "integer"}, "updatedAt": {"type": "string"}, "userId": {"type": "string"}, "username": {"type": "string"}}}, "daisy-server_api_v1.WmsStaffUpdateParams": {"type": "object", "required": ["username"], "properties": {"avatar": {"type": "string", "example": "https://example.com/avatar.png"}, "email": {"type": "string", "example": "<EMAIL>"}, "gender": {"type": "integer", "example": 0}, "nickname": {"type": "string", "maxLength": 64, "example": "管理员"}, "password": {"type": "string", "example": "123456"}, "phone": {"type": "string", "example": "***********"}, "roleIds": {"type": "array", "items": {"type": "integer"}, "example": [1, 2, 3]}, "status": {"type": "boolean", "example": true}, "tenantId": {"type": "integer", "example": 1}, "username": {"type": "string", "maxLength": 64, "example": "admin"}}}, "daisy-server_api_v1.WmsStockCreateParams": {"type": "object", "required": ["areaPath", "itemId", "num", "skuId"], "properties": {"areaPath": {"type": "array", "items": {"type": "integer"}, "example": [1, 2, 3]}, "createdBy": {"type": "string", "example": "管理员"}, "itemId": {"type": "integer", "example": 1}, "num": {"type": "number", "example": 100}, "skuId": {"type": "integer", "example": 1}, "tenantId": {"type": "integer", "example": 1}, "updatedBy": {"type": "string", "example": "管理员"}}}, "daisy-server_api_v1.WmsStockResponse": {"type": "object", "properties": {"areaPath": {"type": "array", "items": {"type": "integer"}}, "createdAt": {"type": "string"}, "createdBy": {"type": "string"}, "id": {"type": "integer"}, "item": {"description": "物料信息", "allOf": [{"$ref": "#/definitions/daisy-server_api_v1.WmsItemResponse"}]}, "itemId": {"type": "integer"}, "num": {"type": "number"}, "sku": {"description": "SKU信息", "allOf": [{"$ref": "#/definitions/daisy-server_api_v1.WmsSkuResponse"}]}, "skuId": {"type": "integer"}, "tenantId": {"type": "integer"}, "updatedAt": {"type": "string"}, "updatedBy": {"type": "string"}}}, "daisy-server_api_v1.WmsStockUpdateParams": {"type": "object", "required": ["areaPath", "itemId", "num", "skuId"], "properties": {"areaPath": {"type": "array", "items": {"type": "integer"}, "example": [1, 2, 3]}, "createdBy": {"type": "string", "example": "管理员"}, "itemId": {"type": "integer", "example": 1}, "num": {"type": "number", "example": 100}, "skuId": {"type": "integer", "example": 1}, "tenantId": {"type": "integer", "example": 1}, "updatedBy": {"type": "string", "example": "管理员"}}}, "daisy-server_api_v1.WmsTransferCreateParams": {"type": "object", "required": ["code", "type"], "properties": {"code": {"type": "string", "maxLength": 64, "example": "TRF202312010001"}, "createdBy": {"type": "string", "example": "管理员"}, "status": {"type": "integer", "example": 1}, "summary": {"type": "string", "example": "库位调拨"}, "tenantId": {"type": "integer", "example": 1}, "type": {"type": "integer", "example": 1}, "updatedBy": {"type": "string", "example": "管理员"}}}, "daisy-server_api_v1.WmsTransferResponse": {"type": "object", "properties": {"code": {"type": "string"}, "createdAt": {"type": "string"}, "createdBy": {"type": "string"}, "id": {"type": "integer"}, "status": {"type": "integer"}, "summary": {"type": "string"}, "tenantId": {"type": "integer"}, "type": {"type": "integer"}, "updatedAt": {"type": "string"}, "updatedBy": {"type": "string"}}}, "daisy-server_api_v1.WmsTransferSkuCreateParams": {"type": "object"}, "daisy-server_api_v1.WmsTransferSkuResponse": {"type": "object", "properties": {"createdAt": {"type": "string"}, "createdBy": {"type": "string"}, "fromAreaNum": {"type": "array", "items": {"type": "integer"}}, "id": {"type": "integer"}, "item": {"description": "物料信息", "allOf": [{"$ref": "#/definitions/daisy-server_api_v1.WmsItemResponse"}]}, "itemId": {"type": "integer"}, "num": {"type": "number"}, "sku": {"description": "规格信息", "allOf": [{"$ref": "#/definitions/daisy-server_api_v1.WmsSkuResponse"}]}, "skuId": {"type": "integer"}, "status": {"type": "boolean"}, "tenantId": {"type": "integer"}, "toAreaNum": {"type": "array", "items": {"type": "integer"}}, "transfer": {"description": "调拨单信息", "allOf": [{"$ref": "#/definitions/daisy-server_api_v1.WmsTransferResponse"}]}, "transferId": {"type": "integer"}, "updatedAt": {"type": "string"}, "updatedBy": {"type": "string"}}}, "daisy-server_api_v1.WmsTransferSkuUpdateParams": {"type": "object"}, "daisy-server_api_v1.WmsTransferUpdateParams": {"type": "object", "required": ["code", "type"], "properties": {"code": {"type": "string", "maxLength": 64, "example": "TRF202312010001"}, "createdBy": {"type": "string", "example": "管理员"}, "status": {"type": "integer", "example": 1}, "summary": {"type": "string", "example": "库位调拨"}, "tenantId": {"type": "integer", "example": 1}, "type": {"type": "integer", "example": 1}, "updatedBy": {"type": "string", "example": "管理员"}}}, "daisy-server_api_v1.WmsUnitCreateParams": {"type": "object", "required": ["name"], "properties": {"createdBy": {"type": "string", "example": "管理员"}, "name": {"type": "string", "maxLength": 64, "example": "个"}, "order": {"type": "integer", "example": 1}, "status": {"type": "boolean", "example": true}, "tenantId": {"type": "integer", "example": 1}, "updatedBy": {"type": "string", "example": "管理员"}}}, "daisy-server_api_v1.WmsUnitResponse": {"type": "object", "properties": {"createdAt": {"type": "string"}, "createdBy": {"type": "string"}, "id": {"type": "integer"}, "name": {"type": "string"}, "order": {"type": "integer"}, "status": {"type": "boolean"}, "tenantId": {"type": "integer"}, "updatedAt": {"type": "string"}, "updatedBy": {"type": "string"}}}, "daisy-server_api_v1.WmsUnitUpdateParams": {"type": "object", "required": ["name"], "properties": {"createdBy": {"type": "string", "example": "管理员"}, "name": {"type": "string", "maxLength": 64, "example": "个"}, "order": {"type": "integer", "example": 1}, "status": {"type": "boolean", "example": true}, "tenantId": {"type": "integer", "example": 1}, "updatedBy": {"type": "string", "example": "管理员"}}}, "daisy-server_pkg_pagination.Result": {"type": "object", "properties": {"records": {"description": "数据列表"}, "total": {"description": "总记录数", "type": "integer"}}}}, "securityDefinitions": {"Bearer": {"type": "<PERSON><PERSON><PERSON><PERSON>", "name": "Authorization", "in": "header"}}}