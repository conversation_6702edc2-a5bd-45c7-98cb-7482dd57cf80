package v1

import "gorm.io/datatypes"

type WmsReceiveSkuCreateParams struct {
	ReceiveId uint           `json:"receiveId" binding:"required" example:"1"`
	ItemId    uint           `json:"itemId" binding:"required" example:"1"`
	SkuId     uint           `json:"skuId" binding:"required" example:"1"`
	BatchNo   string         `json:"batchNo" binding:"max=64" example:"BATCH202312010001"`
	Num       float64        `json:"num" binding:"min=0" example:"100.50"`
	AreaNum   datatypes.JSON `json:"areaNum" swaggertype:"object" example:"[{\"areaPath\":[1,2,3],\"num\":50}]"`
	Status    bool           `json:"status" example:"false"`
	TenantId  uint           `json:"tenantId" example:"1"`
	CreatedBy string         `json:"createdBy" example:"管理员"`
	UpdatedBy string         `json:"updatedBy" example:"管理员"`
}

type WmsReceiveSkuUpdateParams struct {
	WmsReceiveSkuCreateParams
}

type WmsReceiveSkuResponse struct {
	ID        uint                `json:"id"`
	ReceiveId uint                `json:"receiveId"`
	ItemId    uint                `json:"itemId"`
	SkuId     uint                `json:"skuId"`
	BatchNo   string              `json:"batchNo"`
	Num       float64             `json:"num"`
	AreaNum   datatypes.JSON      `json:"areaNum"`
	Status    bool                `json:"status"`
	TenantId  uint                `json:"tenantId"`
	CreatedBy string              `json:"createdBy"`
	UpdatedBy string              `json:"updatedBy"`
	CreatedAt string              `json:"createdAt"`
	UpdatedAt string              `json:"updatedAt"`
	Receive   *WmsReceiveResponse `json:"receive,omitempty"` // 入库单信息
	Item      *WmsItemResponse    `json:"item,omitempty"`    // 物料信息
	Sku       *WmsSkuResponse     `json:"sku,omitempty"`     // 规格信息
}
