package service

import (
	"context"
	v1 "daisy-server/api/v1"
	"daisy-server/internal/model"
	"daisy-server/internal/repository"
	"daisy-server/pkg/pagination"
	"slices"
	"time"

	"github.com/jinzhu/copier"
)

type WmsReceiveService interface {
	Create(ctx context.Context, req *v1.WmsReceiveCreateParams) error
	Update(ctx context.Context, id uint, req *v1.WmsReceiveUpdateParams) error
	Delete(ctx context.Context, id uint) error
	BatchDelete(ctx context.Context, ids []uint) error
	Get(ctx context.Context, id uint, expand []string) (*v1.WmsReceiveResponse, error)
	List(ctx context.Context, params *pagination.Params, expand []string) (*pagination.Result, error)
}

func NewWmsReceiveService(
	service *Service,
	wmsReceiveRepository repository.WmsReceiveRepository,
	wmsPartnerRepository repository.WmsPartnerRepository,
) WmsReceiveService {
	return &wmsReceiveService{
		Service:              service,
		wmsReceiveRepository: wmsReceiveRepository,
		wmsPartnerRepository: wmsPartnerRepository,
	}
}

type wmsReceiveService struct {
	*Service
	wmsReceiveRepository repository.WmsReceiveRepository
	wmsPartnerRepository repository.WmsPartnerRepository
}

// 入库单相关方法实现
func (s *wmsReceiveService) Create(ctx context.Context, req *v1.WmsReceiveCreateParams) error {
	receive := &model.WmsReceive{}
	if err := copier.Copy(receive, req); err != nil {
		return err
	}

	// 获取当前用户信息
	user := ctx.Value("user").(*model.SysUser)

	// 租户ID
	receive.TenantId = user.TenantId

	// 创建人
	if user.Nickname != "" {
		receive.CreatedBy = user.Nickname
	} else {
		receive.CreatedBy = user.Username
	}

	return s.tm.Transaction(ctx, func(ctx context.Context) error {
		if err := s.wmsReceiveRepository.Create(ctx, receive); err != nil {
			return err
		}
		return nil
	})
}

func (s *wmsReceiveService) Update(ctx context.Context, id uint, req *v1.WmsReceiveUpdateParams) error {
	receive, err := s.wmsReceiveRepository.Get(ctx, id)
	if err != nil {
		return err
	}

	// 获取当前用户信息
	user := ctx.Value("user").(*model.SysUser)

	// 过滤非本租户数据
	if receive.TenantId != user.TenantId {
		return v1.ErrForbidden
	}

	if err := copier.Copy(receive, req); err != nil {
		return err
	}

	// 更新人
	if user.Nickname != "" {
		receive.UpdatedBy = user.Nickname
	} else {
		receive.UpdatedBy = user.Username
	}

	return s.tm.Transaction(ctx, func(ctx context.Context) error {
		if err := s.wmsReceiveRepository.Update(ctx, receive); err != nil {
			return err
		}
		return nil
	})
}

func (s *wmsReceiveService) Delete(ctx context.Context, id uint) error {
	// 获取当前用户信息
	user := ctx.Value("user").(*model.SysUser)

	// 是否是该租户数据
	receive, err := s.wmsReceiveRepository.Get(ctx, id)
	if err != nil {
		return err
	}
	if receive.TenantId != user.TenantId {
		return v1.ErrForbidden
	}

	return s.tm.Transaction(ctx, func(ctx context.Context) error {
		if err := s.wmsReceiveRepository.Delete(ctx, id); err != nil {
			return err
		}
		return nil
	})
}

func (s *wmsReceiveService) BatchDelete(ctx context.Context, ids []uint) error {
	// 获取当前用户信息
	user := ctx.Value("user").(*model.SysUser)

	// 获取目标记录
	receives, err := s.wmsReceiveRepository.GetByIds(ctx, ids)
	if err != nil {
		return err
	}

	// 过滤非本租户数据
	var newIds []uint
	for _, receive := range receives {
		if receive.TenantId == user.TenantId {
			newIds = append(newIds, receive.ID)
		}
	}

	return s.tm.Transaction(ctx, func(ctx context.Context) error {
		if err := s.wmsReceiveRepository.BatchDelete(ctx, newIds); err != nil {
			return err
		}
		return nil
	})
}

func (s *wmsReceiveService) Get(ctx context.Context, id uint, expand []string) (*v1.WmsReceiveResponse, error) {
	receive, err := s.wmsReceiveRepository.Get(ctx, id)
	if err != nil {
		return nil, err
	}

	// 获取当前用户信息
	user := ctx.Value("user").(*model.SysUser)

	// 过滤非本租户数据
	if receive.TenantId != user.TenantId {
		return nil, v1.ErrForbidden
	}

	response := &v1.WmsReceiveResponse{}
	if err := copier.Copy(response, receive); err != nil {
		return nil, err
	}

	response.CreatedAt = receive.CreatedAt.Format(time.RFC3339)
	response.UpdatedAt = receive.UpdatedAt.Format(time.RFC3339)

	// 展开供应商信息
	if slices.Contains(expand, "partner") && receive.PartnerId > 0 {
		partner, err := s.wmsPartnerRepository.Get(ctx, receive.PartnerId)
		if err == nil {
			partnerResponse := &v1.WmsPartnerResponse{}
			if err := copier.Copy(partnerResponse, partner); err == nil {
				partnerResponse.CreatedAt = partner.CreatedAt.Format(time.RFC3339)
				partnerResponse.UpdatedAt = partner.UpdatedAt.Format(time.RFC3339)
				response.Partner = partnerResponse
			}
		}
	}

	return response, nil
}

func (s *wmsReceiveService) List(ctx context.Context, params *pagination.Params, expand []string) (*pagination.Result, error) {
	// 获取当前用户信息
	user := ctx.Value("user").(*model.SysUser)

	// 过滤非本租户数据
	params.AddFilter("tenant_id", user.TenantId)

	receives, total, err := s.wmsReceiveRepository.List(ctx, params)
	if err != nil {
		return nil, err
	}

	// 将原始记录转换为API响应格式
	records := make([]*v1.WmsReceiveResponse, 0, len(receives))
	for _, receive := range receives {
		response := &v1.WmsReceiveResponse{}
		if err := copier.Copy(response, receive); err != nil {
			return nil, err
		}

		response.CreatedAt = receive.CreatedAt.Format(time.RFC3339)
		response.UpdatedAt = receive.UpdatedAt.Format(time.RFC3339)

		// 展开供应商信息
		if slices.Contains(expand, "partner") && receive.PartnerId > 0 {
			partner, err := s.wmsPartnerRepository.Get(ctx, receive.PartnerId)
			if err == nil {
				partnerResponse := &v1.WmsPartnerResponse{}
				if err := copier.Copy(partnerResponse, partner); err == nil {
					partnerResponse.CreatedAt = partner.CreatedAt.Format(time.RFC3339)
					partnerResponse.UpdatedAt = partner.UpdatedAt.Format(time.RFC3339)
					response.Partner = partnerResponse
				}
			}
		}

		records = append(records, response)
	}

	return &pagination.Result{
		Records: records,
		Total:   total,
	}, nil
}
