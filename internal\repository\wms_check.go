package repository

import (
	"context"
	v1 "daisy-server/api/v1"
	"daisy-server/internal/model"
	"daisy-server/pkg/pagination"
	"errors"

	"gorm.io/gorm"
)

type WmsCheckRepository interface {
	Create(ctx context.Context, check *model.WmsCheck) error
	Update(ctx context.Context, check *model.WmsCheck) error
	Delete(ctx context.Context, id uint) error
	BatchDelete(ctx context.Context, ids []uint) error
	Get(ctx context.Context, id uint) (*model.WmsCheck, error)
	GetByIds(ctx context.Context, ids []uint) ([]*model.WmsCheck, error)
	List(ctx context.Context, params *pagination.Params) ([]*model.WmsCheck, int64, error)
}

func NewWmsCheckRepository(
	repository *Repository,
) WmsCheckRepository {
	return &wmsCheckRepository{
		Repository: repository,
	}
}

type wmsCheckRepository struct {
	*Repository
}

func (r *wmsCheckRepository) Create(ctx context.Context, check *model.WmsCheck) error {
	if err := r.DB(ctx).Create(check).Error; err != nil {
		return err
	}
	return nil
}

func (r *wmsCheckRepository) Update(ctx context.Context, check *model.WmsCheck) error {
	if err := r.DB(ctx).Save(check).Error; err != nil {
		return err
	}
	return nil
}

func (r *wmsCheckRepository) Delete(ctx context.Context, id uint) error {
	if err := r.DB(ctx).Delete(&model.WmsCheck{}, id).Error; err != nil {
		return err
	}
	return nil
}

func (r *wmsCheckRepository) BatchDelete(ctx context.Context, ids []uint) error {
	if err := r.DB(ctx).Delete(&model.WmsCheck{}, ids).Error; err != nil {
		return err
	}
	return nil
}

func (r *wmsCheckRepository) Get(ctx context.Context, id uint) (*model.WmsCheck, error) {
	var check model.WmsCheck
	if err := r.DB(ctx).First(&check, id).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, v1.ErrNotFound
		}
		return nil, err
	}
	return &check, nil
}

func (r *wmsCheckRepository) GetByIds(ctx context.Context, ids []uint) ([]*model.WmsCheck, error) {
	var checks []*model.WmsCheck
	if len(ids) == 0 {
		return checks, nil
	}
	
	if err := r.DB(ctx).Where("id IN ?", ids).Find(&checks).Error; err != nil {
		return nil, err
	}
	return checks, nil
}

func (r *wmsCheckRepository) List(ctx context.Context, params *pagination.Params) ([]*model.WmsCheck, int64, error) {
	var records []*model.WmsCheck
	var total int64

	db := r.DB(ctx).Model(&model.WmsCheck{})

	// 使用GetResult方法直接获取分页结果
	total, err := params.GetResult(db, &records, &model.WmsCheck{})
	if err != nil {
		return nil, 0, err
	}

	return records, total, nil
}
