package v1

type WmsCheckSkuCreateParams struct {
	CheckId   uint    `json:"checkId" binding:"required" example:"1"`
	ItemId    uint    `json:"itemId" binding:"required" example:"1"`
	SkuId     uint    `json:"skuId" binding:"required" example:"1"`
	Num       float64 `json:"num" binding:"min=0" example:"100.50"`
	Remain    float64 `json:"remain" binding:"min=0" example:"95.30"`
	Status    bool    `json:"status" example:"false"`
	TenantId  uint    `json:"tenantId" example:"1"`
	CreatedBy string  `json:"createdBy" example:"管理员"`
	UpdatedBy string  `json:"updatedBy" example:"管理员"`
}

type WmsCheckSkuUpdateParams struct {
	WmsCheckSkuCreateParams
}

type WmsCheckSkuResponse struct {
	ID        uint              `json:"id"`
	CheckId   uint              `json:"checkId"`
	ItemId    uint              `json:"itemId"`
	SkuId     uint              `json:"skuId"`
	Num       float64           `json:"num"`
	Remain    float64           `json:"remain"`
	Status    bool              `json:"status"`
	TenantId  uint              `json:"tenantId"`
	CreatedBy string            `json:"createdBy"`
	UpdatedBy string            `json:"updatedBy"`
	CreatedAt string            `json:"createdAt"`
	UpdatedAt string            `json:"updatedAt"`
	Check     *WmsCheckResponse `json:"check,omitempty"` // 盘点单信息
	Item      *WmsItemResponse  `json:"item,omitempty"`  // 物料信息
	Sku       *WmsSkuResponse   `json:"sku,omitempty"`   // 规格信息
}
