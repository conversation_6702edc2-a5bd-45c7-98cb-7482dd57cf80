package service

import (
	"context"
	v1 "daisy-server/api/v1"
	"daisy-server/internal/model"
	"daisy-server/internal/repository"
	"daisy-server/pkg/pagination"
	"slices"
	"time"

	"github.com/jinzhu/copier"
)

type WmsTransferSkuService interface {
	Create(ctx context.Context, req *v1.WmsTransferSkuCreateParams) error
	Update(ctx context.Context, id uint, req *v1.WmsTransferSkuUpdateParams) error
	Delete(ctx context.Context, id uint) error
	BatchDelete(ctx context.Context, ids []uint) error
	Get(ctx context.Context, id uint, expand []string) (*v1.WmsTransferSkuResponse, error)
	List(ctx context.Context, params *pagination.Params, expand []string) (*pagination.Result, error)
}

func NewWmsTransferSkuService(
	service *Service,
	wmsTransferSkuRepository repository.WmsTransferSkuRepository,
	wmsTransferRepository repository.WmsTransferRepository,
	wmsItemRepository repository.WmsItemRepository,
	wmsSkuRepository repository.WmsSkuRepository,
) WmsTransferSkuService {
	return &wmsTransferSkuService{
		Service:                  service,
		wmsTransferSkuRepository: wmsTransferSkuRepository,
		wmsTransferRepository:    wmsTransferRepository,
		wmsItemRepository:        wmsItemRepository,
		wmsSkuRepository:         wmsSkuRepository,
	}
}

type wmsTransferSkuService struct {
	*Service
	wmsTransferSkuRepository repository.WmsTransferSkuRepository
	wmsTransferRepository    repository.WmsTransferRepository
	wmsItemRepository        repository.WmsItemRepository
	wmsSkuRepository         repository.WmsSkuRepository
}

// 调拨单物料明细相关方法实现
func (s *wmsTransferSkuService) Create(ctx context.Context, req *v1.WmsTransferSkuCreateParams) error {
	transferSku := &model.WmsTransferSku{}
	if err := copier.Copy(transferSku, req); err != nil {
		return err
	}

	// 获取当前用户信息
	user := ctx.Value("user").(*model.SysUser)

	// 租户ID
	transferSku.TenantId = user.TenantId

	// 创建人
	if user.Nickname != "" {
		transferSku.CreatedBy = user.Nickname
	} else {
		transferSku.CreatedBy = user.Username
	}

	return s.tm.Transaction(ctx, func(ctx context.Context) error {
		if err := s.wmsTransferSkuRepository.Create(ctx, transferSku); err != nil {
			return err
		}
		return nil
	})
}

func (s *wmsTransferSkuService) Update(ctx context.Context, id uint, req *v1.WmsTransferSkuUpdateParams) error {
	transferSku, err := s.wmsTransferSkuRepository.Get(ctx, id)
	if err != nil {
		return err
	}

	// 获取当前用户信息
	user := ctx.Value("user").(*model.SysUser)

	// 过滤非本租户数据
	if transferSku.TenantId != user.TenantId {
		return v1.ErrForbidden
	}

	if err := copier.Copy(transferSku, req); err != nil {
		return err
	}

	// 更新人
	if user.Nickname != "" {
		transferSku.UpdatedBy = user.Nickname
	} else {
		transferSku.UpdatedBy = user.Username
	}

	return s.tm.Transaction(ctx, func(ctx context.Context) error {
		if err := s.wmsTransferSkuRepository.Update(ctx, transferSku); err != nil {
			return err
		}
		return nil
	})
}

func (s *wmsTransferSkuService) Delete(ctx context.Context, id uint) error {
	// 获取当前用户信息
	user := ctx.Value("user").(*model.SysUser)

	// 是否是该租户数据
	transferSku, err := s.wmsTransferSkuRepository.Get(ctx, id)
	if err != nil {
		return err
	}
	if transferSku.TenantId != user.TenantId {
		return v1.ErrForbidden
	}

	return s.tm.Transaction(ctx, func(ctx context.Context) error {
		if err := s.wmsTransferSkuRepository.Delete(ctx, id); err != nil {
			return err
		}
		return nil
	})
}

func (s *wmsTransferSkuService) BatchDelete(ctx context.Context, ids []uint) error {
	// 获取当前用户信息
	user := ctx.Value("user").(*model.SysUser)

	// 获取目标记录
	transferSkus, err := s.wmsTransferSkuRepository.GetByIds(ctx, ids)
	if err != nil {
		return err
	}

	// 过滤非本租户数据
	var newIds []uint
	for _, transferSku := range transferSkus {
		if transferSku.TenantId == user.TenantId {
			newIds = append(newIds, transferSku.ID)
		}
	}

	return s.tm.Transaction(ctx, func(ctx context.Context) error {
		if err := s.wmsTransferSkuRepository.BatchDelete(ctx, newIds); err != nil {
			return err
		}
		return nil
	})
}

func (s *wmsTransferSkuService) Get(ctx context.Context, id uint, expand []string) (*v1.WmsTransferSkuResponse, error) {
	transferSku, err := s.wmsTransferSkuRepository.Get(ctx, id)
	if err != nil {
		return nil, err
	}

	// 获取当前用户信息
	user := ctx.Value("user").(*model.SysUser)

	// 过滤非本租户数据
	if transferSku.TenantId != user.TenantId {
		return nil, v1.ErrForbidden
	}

	response := &v1.WmsTransferSkuResponse{}
	if err := copier.Copy(response, transferSku); err != nil {
		return nil, err
	}

	response.CreatedAt = transferSku.CreatedAt.Format(time.RFC3339)
	response.UpdatedAt = transferSku.UpdatedAt.Format(time.RFC3339)

	// 展开调拨单信息
	if slices.Contains(expand, "transfer") && transferSku.TransferId > 0 {
		transfer, err := s.wmsTransferRepository.Get(ctx, transferSku.TransferId)
		if err == nil {
			transferResponse := &v1.WmsTransferResponse{}
			if err := copier.Copy(transferResponse, transfer); err == nil {
				transferResponse.CreatedAt = transfer.CreatedAt.Format(time.RFC3339)
				transferResponse.UpdatedAt = transfer.UpdatedAt.Format(time.RFC3339)
				response.Transfer = transferResponse
			}
		}
	}

	// 展开物料信息
	if slices.Contains(expand, "item") && transferSku.ItemId > 0 {
		item, err := s.wmsItemRepository.Get(ctx, transferSku.ItemId)
		if err == nil {
			itemResponse := &v1.WmsItemResponse{}
			if err := copier.Copy(itemResponse, item); err == nil {
				itemResponse.CreatedAt = item.CreatedAt.Format(time.RFC3339)
				itemResponse.UpdatedAt = item.UpdatedAt.Format(time.RFC3339)
				response.Item = itemResponse
			}
		}
	}

	// 展开规格信息
	if slices.Contains(expand, "sku") && transferSku.SkuId > 0 {
		sku, err := s.wmsSkuRepository.Get(ctx, transferSku.SkuId)
		if err == nil {
			skuResponse := &v1.WmsSkuResponse{}
			if err := copier.Copy(skuResponse, sku); err == nil {
				skuResponse.CreatedAt = sku.CreatedAt.Format(time.RFC3339)
				skuResponse.UpdatedAt = sku.UpdatedAt.Format(time.RFC3339)
				response.Sku = skuResponse
			}
		}
	}

	return response, nil
}

func (s *wmsTransferSkuService) List(ctx context.Context, params *pagination.Params, expand []string) (*pagination.Result, error) {
	// 获取当前用户信息
	user := ctx.Value("user").(*model.SysUser)

	// 过滤非本租户数据
	params.AddFilter("tenant_id", user.TenantId)

	transferSkus, total, err := s.wmsTransferSkuRepository.List(ctx, params)
	if err != nil {
		return nil, err
	}

	// 将原始记录转换为API响应格式
	records := make([]*v1.WmsTransferSkuResponse, 0, len(transferSkus))
	for _, transferSku := range transferSkus {
		response := &v1.WmsTransferSkuResponse{}
		if err := copier.Copy(response, transferSku); err != nil {
			return nil, err
		}

		response.CreatedAt = transferSku.CreatedAt.Format(time.RFC3339)
		response.UpdatedAt = transferSku.UpdatedAt.Format(time.RFC3339)

		// 展开调拨单信息
		if slices.Contains(expand, "transfer") && transferSku.TransferId > 0 {
			transfer, err := s.wmsTransferRepository.Get(ctx, transferSku.TransferId)
			if err == nil {
				transferResponse := &v1.WmsTransferResponse{}
				if err := copier.Copy(transferResponse, transfer); err == nil {
					transferResponse.CreatedAt = transfer.CreatedAt.Format(time.RFC3339)
					transferResponse.UpdatedAt = transfer.UpdatedAt.Format(time.RFC3339)
					response.Transfer = transferResponse
				}
			}
		}

		// 展开物料信息
		if slices.Contains(expand, "item") && transferSku.ItemId > 0 {
			item, err := s.wmsItemRepository.Get(ctx, transferSku.ItemId)
			if err == nil {
				itemResponse := &v1.WmsItemResponse{}
				if err := copier.Copy(itemResponse, item); err == nil {
					itemResponse.CreatedAt = item.CreatedAt.Format(time.RFC3339)
					itemResponse.UpdatedAt = item.UpdatedAt.Format(time.RFC3339)
					response.Item = itemResponse
				}
			}
		}

		// 展开规格信息
		if slices.Contains(expand, "sku") && transferSku.SkuId > 0 {
			sku, err := s.wmsSkuRepository.Get(ctx, transferSku.SkuId)
			if err == nil {
				skuResponse := &v1.WmsSkuResponse{}
				if err := copier.Copy(skuResponse, sku); err == nil {
					skuResponse.CreatedAt = sku.CreatedAt.Format(time.RFC3339)
					skuResponse.UpdatedAt = sku.UpdatedAt.Format(time.RFC3339)
					response.Sku = skuResponse
				}
			}
		}

		records = append(records, response)
	}

	return &pagination.Result{
		Records: records,
		Total:   total,
	}, nil
}
