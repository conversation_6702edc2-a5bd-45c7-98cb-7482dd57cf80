package service

import (
	"context"
	v1 "daisy-server/api/v1"
	"daisy-server/internal/model"
	"daisy-server/internal/repository"
	"daisy-server/pkg/pagination"
	"slices"
	"time"

	"github.com/jinzhu/copier"
)

type WmsCheckSkuService interface {
	Create(ctx context.Context, req *v1.WmsCheckSkuCreateParams) error
	Update(ctx context.Context, id uint, req *v1.WmsCheckSkuUpdateParams) error
	Delete(ctx context.Context, id uint) error
	BatchDelete(ctx context.Context, ids []uint) error
	Get(ctx context.Context, id uint, expand []string) (*v1.WmsCheckSkuResponse, error)
	List(ctx context.Context, params *pagination.Params, expand []string) (*pagination.Result, error)
}

func NewWmsCheckSkuService(
	service *Service,
	wmsCheckSkuRepository repository.WmsCheckSkuRepository,
	wmsCheckRepository repository.WmsCheckRepository,
	wmsItemRepository repository.WmsItemRepository,
	wmsSkuRepository repository.WmsSkuRepository,
) WmsCheckSkuService {
	return &wmsCheckSkuService{
		Service:               service,
		wmsCheckSkuRepository: wmsCheckSkuRepository,
		wmsCheckRepository:    wmsCheckRepository,
		wmsItemRepository:     wmsItemRepository,
		wmsSkuRepository:      wmsSkuRepository,
	}
}

type wmsCheckSkuService struct {
	*Service
	wmsCheckSkuRepository repository.WmsCheckSkuRepository
	wmsCheckRepository    repository.WmsCheckRepository
	wmsItemRepository     repository.WmsItemRepository
	wmsSkuRepository      repository.WmsSkuRepository
}

// 盘点单物料明细相关方法实现
func (s *wmsCheckSkuService) Create(ctx context.Context, req *v1.WmsCheckSkuCreateParams) error {
	checkSku := &model.WmsCheckSku{}
	if err := copier.Copy(checkSku, req); err != nil {
		return err
	}

	// 获取当前用户信息
	user := ctx.Value("user").(*model.SysUser)

	// 租户ID
	checkSku.TenantId = user.TenantId

	// 创建人
	if user.Nickname != "" {
		checkSku.CreatedBy = user.Nickname
	} else {
		checkSku.CreatedBy = user.Username
	}

	return s.tm.Transaction(ctx, func(ctx context.Context) error {
		if err := s.wmsCheckSkuRepository.Create(ctx, checkSku); err != nil {
			return err
		}
		return nil
	})
}

func (s *wmsCheckSkuService) Update(ctx context.Context, id uint, req *v1.WmsCheckSkuUpdateParams) error {
	checkSku, err := s.wmsCheckSkuRepository.Get(ctx, id)
	if err != nil {
		return err
	}

	// 获取当前用户信息
	user := ctx.Value("user").(*model.SysUser)

	// 过滤非本租户数据
	if checkSku.TenantId != user.TenantId {
		return v1.ErrForbidden
	}

	if err := copier.Copy(checkSku, req); err != nil {
		return err
	}

	// 更新人
	if user.Nickname != "" {
		checkSku.UpdatedBy = user.Nickname
	} else {
		checkSku.UpdatedBy = user.Username
	}

	return s.tm.Transaction(ctx, func(ctx context.Context) error {
		if err := s.wmsCheckSkuRepository.Update(ctx, checkSku); err != nil {
			return err
		}
		return nil
	})
}

func (s *wmsCheckSkuService) Delete(ctx context.Context, id uint) error {
	// 获取当前用户信息
	user := ctx.Value("user").(*model.SysUser)

	// 是否是该租户数据
	checkSku, err := s.wmsCheckSkuRepository.Get(ctx, id)
	if err != nil {
		return err
	}
	if checkSku.TenantId != user.TenantId {
		return v1.ErrForbidden
	}

	return s.tm.Transaction(ctx, func(ctx context.Context) error {
		if err := s.wmsCheckSkuRepository.Delete(ctx, id); err != nil {
			return err
		}
		return nil
	})
}

func (s *wmsCheckSkuService) BatchDelete(ctx context.Context, ids []uint) error {
	// 获取当前用户信息
	user := ctx.Value("user").(*model.SysUser)

	// 获取目标记录
	checkSkus, err := s.wmsCheckSkuRepository.GetByIds(ctx, ids)
	if err != nil {
		return err
	}

	// 过滤非本租户数据
	var newIds []uint
	for _, checkSku := range checkSkus {
		if checkSku.TenantId == user.TenantId {
			newIds = append(newIds, checkSku.ID)
		}
	}

	return s.tm.Transaction(ctx, func(ctx context.Context) error {
		if err := s.wmsCheckSkuRepository.BatchDelete(ctx, newIds); err != nil {
			return err
		}
		return nil
	})
}

func (s *wmsCheckSkuService) Get(ctx context.Context, id uint, expand []string) (*v1.WmsCheckSkuResponse, error) {
	checkSku, err := s.wmsCheckSkuRepository.Get(ctx, id)
	if err != nil {
		return nil, err
	}

	// 获取当前用户信息
	user := ctx.Value("user").(*model.SysUser)

	// 过滤非本租户数据
	if checkSku.TenantId != user.TenantId {
		return nil, v1.ErrForbidden
	}

	response := &v1.WmsCheckSkuResponse{}
	if err := copier.Copy(response, checkSku); err != nil {
		return nil, err
	}

	response.CreatedAt = checkSku.CreatedAt.Format(time.RFC3339)
	response.UpdatedAt = checkSku.UpdatedAt.Format(time.RFC3339)

	// 展开盘点单信息
	if slices.Contains(expand, "check") && checkSku.CheckId > 0 {
		check, err := s.wmsCheckRepository.Get(ctx, checkSku.CheckId)
		if err == nil {
			checkResponse := &v1.WmsCheckResponse{}
			if err := copier.Copy(checkResponse, check); err == nil {
				checkResponse.CreatedAt = check.CreatedAt.Format(time.RFC3339)
				checkResponse.UpdatedAt = check.UpdatedAt.Format(time.RFC3339)
				response.Check = checkResponse
			}
		}
	}

	// 展开物料信息
	if slices.Contains(expand, "item") && checkSku.ItemId > 0 {
		item, err := s.wmsItemRepository.Get(ctx, checkSku.ItemId)
		if err == nil {
			itemResponse := &v1.WmsItemResponse{}
			if err := copier.Copy(itemResponse, item); err == nil {
				itemResponse.CreatedAt = item.CreatedAt.Format(time.RFC3339)
				itemResponse.UpdatedAt = item.UpdatedAt.Format(time.RFC3339)
				response.Item = itemResponse
			}
		}
	}

	// 展开规格信息
	if slices.Contains(expand, "sku") && checkSku.SkuId > 0 {
		sku, err := s.wmsSkuRepository.Get(ctx, checkSku.SkuId)
		if err == nil {
			skuResponse := &v1.WmsSkuResponse{}
			if err := copier.Copy(skuResponse, sku); err == nil {
				skuResponse.CreatedAt = sku.CreatedAt.Format(time.RFC3339)
				skuResponse.UpdatedAt = sku.UpdatedAt.Format(time.RFC3339)
				response.Sku = skuResponse
			}
		}
	}

	return response, nil
}

func (s *wmsCheckSkuService) List(ctx context.Context, params *pagination.Params, expand []string) (*pagination.Result, error) {
	// 获取当前用户信息
	user := ctx.Value("user").(*model.SysUser)

	// 过滤非本租户数据
	params.AddFilter("tenant_id", user.TenantId)

	checkSkus, total, err := s.wmsCheckSkuRepository.List(ctx, params)
	if err != nil {
		return nil, err
	}

	// 将原始记录转换为API响应格式
	records := make([]*v1.WmsCheckSkuResponse, 0, len(checkSkus))
	for _, checkSku := range checkSkus {
		response := &v1.WmsCheckSkuResponse{}
		if err := copier.Copy(response, checkSku); err != nil {
			return nil, err
		}

		response.CreatedAt = checkSku.CreatedAt.Format(time.RFC3339)
		response.UpdatedAt = checkSku.UpdatedAt.Format(time.RFC3339)

		// 展开盘点单信息
		if slices.Contains(expand, "check") && checkSku.CheckId > 0 {
			check, err := s.wmsCheckRepository.Get(ctx, checkSku.CheckId)
			if err == nil {
				checkResponse := &v1.WmsCheckResponse{}
				if err := copier.Copy(checkResponse, check); err == nil {
					checkResponse.CreatedAt = check.CreatedAt.Format(time.RFC3339)
					checkResponse.UpdatedAt = check.UpdatedAt.Format(time.RFC3339)
					response.Check = checkResponse
				}
			}
		}

		// 展开物料信息
		if slices.Contains(expand, "item") && checkSku.ItemId > 0 {
			item, err := s.wmsItemRepository.Get(ctx, checkSku.ItemId)
			if err == nil {
				itemResponse := &v1.WmsItemResponse{}
				if err := copier.Copy(itemResponse, item); err == nil {
					itemResponse.CreatedAt = item.CreatedAt.Format(time.RFC3339)
					itemResponse.UpdatedAt = item.UpdatedAt.Format(time.RFC3339)
					response.Item = itemResponse
				}
			}
		}

		// 展开规格信息
		if slices.Contains(expand, "sku") && checkSku.SkuId > 0 {
			sku, err := s.wmsSkuRepository.Get(ctx, checkSku.SkuId)
			if err == nil {
				skuResponse := &v1.WmsSkuResponse{}
				if err := copier.Copy(skuResponse, sku); err == nil {
					skuResponse.CreatedAt = sku.CreatedAt.Format(time.RFC3339)
					skuResponse.UpdatedAt = sku.UpdatedAt.Format(time.RFC3339)
					response.Sku = skuResponse
				}
			}
		}

		records = append(records, response)
	}

	return &pagination.Result{
		Records: records,
		Total:   total,
	}, nil
}
