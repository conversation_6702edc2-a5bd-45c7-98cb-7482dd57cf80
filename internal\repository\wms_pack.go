package repository

import (
	"context"
	v1 "daisy-server/api/v1"
	"daisy-server/internal/model"
	"daisy-server/pkg/pagination"
	"errors"

	"gorm.io/gorm"
)

type WmsPackRepository interface {
	Create(ctx context.Context, pack *model.WmsPack) error
	Update(ctx context.Context, pack *model.WmsPack) error
	Delete(ctx context.Context, id uint) error
	BatchDelete(ctx context.Context, ids []uint) error
	Get(ctx context.Context, id uint) (*model.WmsPack, error)
	GetByIds(ctx context.Context, ids []uint) ([]*model.WmsPack, error)
	List(ctx context.Context, params *pagination.Params) ([]*model.WmsPack, int64, error)
}

func NewWmsPackRepository(
	repository *Repository,
) WmsPackRepository {
	return &wmsPackRepository{
		Repository: repository,
	}
}

type wmsPackRepository struct {
	*Repository
}

func (r *wmsPackRepository) Create(ctx context.Context, pack *model.WmsPack) error {
	if err := r.DB(ctx).Create(pack).Error; err != nil {
		return err
	}
	return nil
}

func (r *wmsPackRepository) Update(ctx context.Context, pack *model.WmsPack) error {
	if err := r.DB(ctx).Save(pack).Error; err != nil {
		return err
	}
	return nil
}

func (r *wmsPackRepository) Delete(ctx context.Context, id uint) error {
	if err := r.DB(ctx).Delete(&model.WmsPack{}, id).Error; err != nil {
		return err
	}
	return nil
}

func (r *wmsPackRepository) BatchDelete(ctx context.Context, ids []uint) error {
	if err := r.DB(ctx).Delete(&model.WmsPack{}, ids).Error; err != nil {
		return err
	}
	return nil
}

func (r *wmsPackRepository) Get(ctx context.Context, id uint) (*model.WmsPack, error) {
	var pack model.WmsPack
	if err := r.DB(ctx).First(&pack, id).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, v1.ErrNotFound
		}
		return nil, err
	}
	return &pack, nil
}

func (r *wmsPackRepository) GetByIds(ctx context.Context, ids []uint) ([]*model.WmsPack, error) {
	var packs []*model.WmsPack
	if len(ids) == 0 {
		return packs, nil
	}
	
	if err := r.DB(ctx).Where("id IN ?", ids).Find(&packs).Error; err != nil {
		return nil, err
	}
	return packs, nil
}

func (r *wmsPackRepository) List(ctx context.Context, params *pagination.Params) ([]*model.WmsPack, int64, error) {
	var records []*model.WmsPack
	var total int64

	db := r.DB(ctx).Model(&model.WmsPack{})

	// 使用GetResult方法直接获取分页结果
	total, err := params.GetResult(db, &records, &model.WmsPack{})
	if err != nil {
		return nil, 0, err
	}

	return records, total, nil
}
