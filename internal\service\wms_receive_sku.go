package service

import (
	"context"
	v1 "daisy-server/api/v1"
	"daisy-server/internal/model"
	"daisy-server/internal/repository"
	"daisy-server/pkg/pagination"
	"slices"
	"time"

	"github.com/jinzhu/copier"
)

type WmsReceiveSkuService interface {
	Create(ctx context.Context, req *v1.WmsReceiveSkuCreateParams) error
	Update(ctx context.Context, id uint, req *v1.WmsReceiveSkuUpdateParams) error
	Delete(ctx context.Context, id uint) error
	BatchDelete(ctx context.Context, ids []uint) error
	Get(ctx context.Context, id uint, expand []string) (*v1.WmsReceiveSkuResponse, error)
	List(ctx context.Context, params *pagination.Params, expand []string) (*pagination.Result, error)
}

func NewWmsReceiveSkuService(
	service *Service,
	wmsReceiveSkuRepository repository.WmsReceiveSkuRepository,
	wmsReceiveRepository repository.WmsReceiveRepository,
	wmsItemRepository repository.WmsItemRepository,
	wmsSkuRepository repository.WmsSkuRepository,
) WmsReceiveSkuService {
	return &wmsReceiveSkuService{
		Service:                 service,
		wmsReceiveSkuRepository: wmsReceiveSkuRepository,
		wmsReceiveRepository:    wmsReceiveRepository,
		wmsItemRepository:       wmsItemRepository,
		wmsSkuRepository:        wmsSkuRepository,
	}
}

type wmsReceiveSkuService struct {
	*Service
	wmsReceiveSkuRepository repository.WmsReceiveSkuRepository
	wmsReceiveRepository    repository.WmsReceiveRepository
	wmsItemRepository       repository.WmsItemRepository
	wmsSkuRepository        repository.WmsSkuRepository
}

// 入库单物料明细相关方法实现
func (s *wmsReceiveSkuService) Create(ctx context.Context, req *v1.WmsReceiveSkuCreateParams) error {
	receiveSku := &model.WmsReceiveSku{}
	if err := copier.Copy(receiveSku, req); err != nil {
		return err
	}

	// 获取当前用户信息
	user := ctx.Value("user").(*model.SysUser)

	// 租户ID
	receiveSku.TenantId = user.TenantId

	// 创建人
	if user.Nickname != "" {
		receiveSku.CreatedBy = user.Nickname
	} else {
		receiveSku.CreatedBy = user.Username
	}

	return s.tm.Transaction(ctx, func(ctx context.Context) error {
		if err := s.wmsReceiveSkuRepository.Create(ctx, receiveSku); err != nil {
			return err
		}
		return nil
	})
}

func (s *wmsReceiveSkuService) Update(ctx context.Context, id uint, req *v1.WmsReceiveSkuUpdateParams) error {
	receiveSku, err := s.wmsReceiveSkuRepository.Get(ctx, id)
	if err != nil {
		return err
	}

	// 获取当前用户信息
	user := ctx.Value("user").(*model.SysUser)

	// 过滤非本租户数据
	if receiveSku.TenantId != user.TenantId {
		return v1.ErrForbidden
	}

	if err := copier.Copy(receiveSku, req); err != nil {
		return err
	}

	// 更新人
	if user.Nickname != "" {
		receiveSku.UpdatedBy = user.Nickname
	} else {
		receiveSku.UpdatedBy = user.Username
	}

	return s.tm.Transaction(ctx, func(ctx context.Context) error {
		if err := s.wmsReceiveSkuRepository.Update(ctx, receiveSku); err != nil {
			return err
		}
		return nil
	})
}

func (s *wmsReceiveSkuService) Delete(ctx context.Context, id uint) error {
	// 获取当前用户信息
	user := ctx.Value("user").(*model.SysUser)

	// 是否是该租户数据
	receiveSku, err := s.wmsReceiveSkuRepository.Get(ctx, id)
	if err != nil {
		return err
	}
	if receiveSku.TenantId != user.TenantId {
		return v1.ErrForbidden
	}

	return s.tm.Transaction(ctx, func(ctx context.Context) error {
		if err := s.wmsReceiveSkuRepository.Delete(ctx, id); err != nil {
			return err
		}
		return nil
	})
}

func (s *wmsReceiveSkuService) BatchDelete(ctx context.Context, ids []uint) error {
	// 获取当前用户信息
	user := ctx.Value("user").(*model.SysUser)

	// 获取目标记录
	receiveSkus, err := s.wmsReceiveSkuRepository.GetByIds(ctx, ids)
	if err != nil {
		return err
	}

	// 过滤非本租户数据
	var newIds []uint
	for _, receiveSku := range receiveSkus {
		if receiveSku.TenantId == user.TenantId {
			newIds = append(newIds, receiveSku.ID)
		}
	}

	return s.tm.Transaction(ctx, func(ctx context.Context) error {
		if err := s.wmsReceiveSkuRepository.BatchDelete(ctx, newIds); err != nil {
			return err
		}
		return nil
	})
}

func (s *wmsReceiveSkuService) Get(ctx context.Context, id uint, expand []string) (*v1.WmsReceiveSkuResponse, error) {
	receiveSku, err := s.wmsReceiveSkuRepository.Get(ctx, id)
	if err != nil {
		return nil, err
	}

	// 获取当前用户信息
	user := ctx.Value("user").(*model.SysUser)

	// 过滤非本租户数据
	if receiveSku.TenantId != user.TenantId {
		return nil, v1.ErrForbidden
	}

	response := &v1.WmsReceiveSkuResponse{}
	if err := copier.Copy(response, receiveSku); err != nil {
		return nil, err
	}

	response.CreatedAt = receiveSku.CreatedAt.Format(time.RFC3339)
	response.UpdatedAt = receiveSku.UpdatedAt.Format(time.RFC3339)

	// 展开入库单信息
	if slices.Contains(expand, "receive") && receiveSku.ReceiveId > 0 {
		receive, err := s.wmsReceiveRepository.Get(ctx, receiveSku.ReceiveId)
		if err == nil {
			receiveResponse := &v1.WmsReceiveResponse{}
			if err := copier.Copy(receiveResponse, receive); err == nil {
				receiveResponse.CreatedAt = receive.CreatedAt.Format(time.RFC3339)
				receiveResponse.UpdatedAt = receive.UpdatedAt.Format(time.RFC3339)
				response.Receive = receiveResponse
			}
		}
	}

	// 展开物料信息
	if slices.Contains(expand, "item") && receiveSku.ItemId > 0 {
		item, err := s.wmsItemRepository.Get(ctx, receiveSku.ItemId)
		if err == nil {
			itemResponse := &v1.WmsItemResponse{}
			if err := copier.Copy(itemResponse, item); err == nil {
				itemResponse.CreatedAt = item.CreatedAt.Format(time.RFC3339)
				itemResponse.UpdatedAt = item.UpdatedAt.Format(time.RFC3339)
				response.Item = itemResponse
			}
		}
	}

	// 展开规格信息
	if slices.Contains(expand, "sku") && receiveSku.SkuId > 0 {
		sku, err := s.wmsSkuRepository.Get(ctx, receiveSku.SkuId)
		if err == nil {
			skuResponse := &v1.WmsSkuResponse{}
			if err := copier.Copy(skuResponse, sku); err == nil {
				skuResponse.CreatedAt = sku.CreatedAt.Format(time.RFC3339)
				skuResponse.UpdatedAt = sku.UpdatedAt.Format(time.RFC3339)
				response.Sku = skuResponse
			}
		}
	}

	return response, nil
}

func (s *wmsReceiveSkuService) List(ctx context.Context, params *pagination.Params, expand []string) (*pagination.Result, error) {
	// 获取当前用户信息
	user := ctx.Value("user").(*model.SysUser)

	// 过滤非本租户数据
	params.AddFilter("tenant_id", user.TenantId)

	receiveSkus, total, err := s.wmsReceiveSkuRepository.List(ctx, params)
	if err != nil {
		return nil, err
	}

	// 将原始记录转换为API响应格式
	records := make([]*v1.WmsReceiveSkuResponse, 0, len(receiveSkus))
	for _, receiveSku := range receiveSkus {
		response := &v1.WmsReceiveSkuResponse{}
		if err := copier.Copy(response, receiveSku); err != nil {
			return nil, err
		}

		response.CreatedAt = receiveSku.CreatedAt.Format(time.RFC3339)
		response.UpdatedAt = receiveSku.UpdatedAt.Format(time.RFC3339)

		// 展开入库单信息
		if slices.Contains(expand, "receive") && receiveSku.ReceiveId > 0 {
			receive, err := s.wmsReceiveRepository.Get(ctx, receiveSku.ReceiveId)
			if err == nil {
				receiveResponse := &v1.WmsReceiveResponse{}
				if err := copier.Copy(receiveResponse, receive); err == nil {
					receiveResponse.CreatedAt = receive.CreatedAt.Format(time.RFC3339)
					receiveResponse.UpdatedAt = receive.UpdatedAt.Format(time.RFC3339)
					response.Receive = receiveResponse
				}
			}
		}

		// 展开物料信息
		if slices.Contains(expand, "item") && receiveSku.ItemId > 0 {
			item, err := s.wmsItemRepository.Get(ctx, receiveSku.ItemId)
			if err == nil {
				itemResponse := &v1.WmsItemResponse{}
				if err := copier.Copy(itemResponse, item); err == nil {
					itemResponse.CreatedAt = item.CreatedAt.Format(time.RFC3339)
					itemResponse.UpdatedAt = item.UpdatedAt.Format(time.RFC3339)
					response.Item = itemResponse
				}
			}
		}

		// 展开规格信息
		if slices.Contains(expand, "sku") && receiveSku.SkuId > 0 {
			sku, err := s.wmsSkuRepository.Get(ctx, receiveSku.SkuId)
			if err == nil {
				skuResponse := &v1.WmsSkuResponse{}
				if err := copier.Copy(skuResponse, sku); err == nil {
					skuResponse.CreatedAt = sku.CreatedAt.Format(time.RFC3339)
					skuResponse.UpdatedAt = sku.UpdatedAt.Format(time.RFC3339)
					response.Sku = skuResponse
				}
			}
		}

		records = append(records, response)
	}

	return &pagination.Result{
		Records: records,
		Total:   total,
	}, nil
}
