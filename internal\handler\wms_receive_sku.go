package handler

import (
	v1 "daisy-server/api/v1"
	"daisy-server/internal/service"
	"daisy-server/pkg/pagination"
	"net/http"
	"strconv"
	"strings"

	"github.com/gin-gonic/gin"
)

type WmsReceiveSkuHandler struct {
	*Handler
	wmsReceiveSkuService service.WmsReceiveSkuService
}

func NewWmsReceiveSkuHandler(
	handler *Handler,
	wmsReceiveSkuService service.WmsReceiveSkuService,
) *WmsReceiveSkuHandler {
	return &WmsReceiveSkuHandler{
		Handler:              handler,
		wmsReceiveSkuService: wmsReceiveSkuService,
	}
}

// Create godoc
// @Summary 创建入库单物料规格
// @Schemes
// @Description 创建新的入库单物料规格记录
// @Tags 仓储模块,入库单物料规格管理
// @Accept json
// @Produce json
// @Security Bearer
// @Param request body v1.WmsReceiveSkuCreateParams true "入库单物料规格信息"
// @Success 200 {object} v1.Response
// @Router /wms/receive-skus [post]
func (h *WmsReceiveSkuHandler) Create(ctx *gin.Context) {
	var req v1.WmsReceiveSkuCreateParams
	if err := ctx.ShouldBindJSON(&req); err != nil {
		v1.HandleError(ctx, http.StatusBadRequest, v1.ErrBadRequest, nil)
		return
	}

	if err := h.wmsReceiveSkuService.Create(ctx, &req); err != nil {
		v1.HandleError(ctx, http.StatusInternalServerError, err, nil)
		return
	}

	v1.HandleSuccess(ctx, nil)
}

// Update godoc
// @Summary 更新入库单物料规格
// @Schemes
// @Description 更新指定ID的入库单物料规格信息
// @Tags 仓储模块,入库单物料规格管理
// @Accept json
// @Produce json
// @Security Bearer
// @Param id path int true "入库单物料规格ID"
// @Param request body v1.WmsReceiveSkuUpdateParams true "入库单物料规格信息"
// @Success 200 {object} v1.Response
// @Router /wms/receive-skus/{id} [patch]
func (h *WmsReceiveSkuHandler) Update(ctx *gin.Context) {
	idStr := ctx.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		v1.HandleError(ctx, http.StatusBadRequest, v1.ErrBadRequest, nil)
		return
	}

	var req v1.WmsReceiveSkuUpdateParams
	if err := ctx.ShouldBindJSON(&req); err != nil {
		v1.HandleError(ctx, http.StatusBadRequest, v1.ErrBadRequest, nil)
		return
	}

	if err := h.wmsReceiveSkuService.Update(ctx, uint(id), &req); err != nil {
		v1.HandleError(ctx, http.StatusInternalServerError, err, nil)
		return
	}

	v1.HandleSuccess(ctx, nil)
}

// Delete godoc
// @Summary 删除入库单物料规格
// @Schemes
// @Description 删除指定ID的入库单物料规格
// @Tags 仓储模块,入库单物料规格管理
// @Accept json
// @Produce json
// @Security Bearer
// @Param id path int true "入库单物料规格ID"
// @Success 200 {object} v1.Response
// @Router /wms/receive-skus/{id} [delete]
func (h *WmsReceiveSkuHandler) Delete(ctx *gin.Context) {
	idStr := ctx.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		v1.HandleError(ctx, http.StatusBadRequest, v1.ErrBadRequest, nil)
		return
	}

	if err := h.wmsReceiveSkuService.Delete(ctx, uint(id)); err != nil {
		v1.HandleError(ctx, http.StatusInternalServerError, err, nil)
		return
	}

	v1.HandleSuccess(ctx, nil)
}

// BatchDelete godoc
// @Summary 批量删除入库单物料规格
// @Schemes
// @Description 批量删除指定IDs的入库单物料规格
// @Tags 仓储模块,入库单物料规格管理
// @Accept json
// @Produce json
// @Security Bearer
// @Param request body v1.BatchDeleteParams true "入库单物料规格IDs"
// @Success 200 {object} v1.Response
// @Router /wms/receive-skus [delete]
func (h *WmsReceiveSkuHandler) BatchDelete(ctx *gin.Context) {
	var req v1.BatchDeleteParams
	if err := ctx.ShouldBindJSON(&req); err != nil {
		v1.HandleError(ctx, http.StatusBadRequest, v1.ErrBadRequest, nil)
		return
	}

	if err := h.wmsReceiveSkuService.BatchDelete(ctx, req.IDs); err != nil {
		v1.HandleError(ctx, http.StatusInternalServerError, err, nil)
		return
	}

	v1.HandleSuccess(ctx, nil)
}

// Get godoc
// @Summary 获取入库单物料规格
// @Schemes
// @Description 获取指定ID的入库单物料规格信息
// @Tags 仓储模块,入库单物料规格管理
// @Accept json
// @Produce json
// @Security Bearer
// @Param id path int true "入库单物料规格ID"
// @Param _expand query string false "展开关联信息，支持: receive,item,sku" example:"receive,item,sku"
// @Success 200 {object} v1.Response{data=v1.WmsReceiveSkuResponse}
// @Router /wms/receive-skus/{id} [get]
func (h *WmsReceiveSkuHandler) Get(ctx *gin.Context) {
	idStr := ctx.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		v1.HandleError(ctx, http.StatusBadRequest, v1.ErrBadRequest, nil)
		return
	}

	receiveSku, err := h.wmsReceiveSkuService.Get(ctx, uint(id), strings.Split(ctx.Query("_expand"), ","))
	if err != nil {
		v1.HandleError(ctx, http.StatusInternalServerError, err, nil)
		return
	}

	v1.HandleSuccess(ctx, receiveSku)
}

// List godoc
// @Summary 获取入库单物料规格列表
// @Schemes
// @Description 分页获取入库单物料规格列表
// @Tags 仓储模块,入库单物料规格管理
// @Accept json
// @Produce json
// @Security Bearer
// @Param _page query int false "当前页码(默认值: 1)" example:"1"
// @Param _limit query int false "每页数量(默认值: 10)" example:"10"
// @Param _sort query string false "排序字段，多个字段用逗号分隔" example:"id"
// @Param _order query string false "排序方式，asc或desc，多个方式用逗号分隔" example:"desc"
// @Param q query string false "全文搜索" example:"关键词"
// @Param status query bool false "状态筛选" example:"true"
// @Param receiveId query int false "入库单ID筛选" example:"1"
// @Param itemId query int false "物料ID筛选" example:"1"
// @Param skuId query int false "规格ID筛选" example:"1"
// @Param batchNo query string false "批次号筛选" example:"BATCH202312010001"
// @Param _expand query string false "展开关联信息，支持: receive,item,sku" example:"receive,item,sku"
// @Success 200 {object} v1.Response{data=pagination.Result{records=[]v1.WmsReceiveSkuResponse}}
// @Router /wms/receive-skus [get]
func (h *WmsReceiveSkuHandler) List(ctx *gin.Context) {
	var params pagination.Params
	if err := ctx.ShouldBindQuery(&params); err != nil {
		v1.HandleError(ctx, http.StatusBadRequest, v1.ErrBadRequest, nil)
		return
	}

	// 状态筛选
	if status := ctx.DefaultQuery("status", ""); status != "" {
		params.AddFilter("status", status)
	}

	// 入库单ID筛选
	if receiveId := ctx.DefaultQuery("receiveId", ""); receiveId != "" {
		params.AddFilter("receive_id", receiveId)
	}

	// 物料ID筛选
	if itemId := ctx.DefaultQuery("itemId", ""); itemId != "" {
		params.AddFilter("item_id", itemId)
	}

	// 规格ID筛选
	if skuId := ctx.DefaultQuery("skuId", ""); skuId != "" {
		params.AddFilter("sku_id", skuId)
	}

	// 批次号筛选
	if batchNo := ctx.DefaultQuery("batchNo", ""); batchNo != "" {
		params.AddFilter("batch_no_like", batchNo)
	}

	result, err := h.wmsReceiveSkuService.List(ctx, &params, strings.Split(ctx.Query("_expand"), ","))
	if err != nil {
		v1.HandleError(ctx, http.StatusInternalServerError, err, nil)
		return
	}

	v1.HandleSuccess(ctx, result)
}
