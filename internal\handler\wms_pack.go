package handler

import (
	v1 "daisy-server/api/v1"
	"daisy-server/internal/service"
	"daisy-server/pkg/pagination"
	"net/http"
	"strconv"
	"strings"

	"github.com/gin-gonic/gin"
)

type WmsPackHandler struct {
	*Handler
	wmsPackService service.WmsPackService
}

func NewWmsPackHandler(
	handler *Handler,
	wmsPackService service.WmsPackService,
) *WmsPackHandler {
	return &WmsPackHandler{
		Handler:        handler,
		wmsPackService: wmsPackService,
	}
}

// Create godoc
// @Summary 创建包装单
// @Schemes
// @Description 创建新的包装单记录
// @Tags 仓储模块,包装单管理
// @Accept json
// @Produce json
// @Security Bearer
// @Param request body v1.WmsPackCreateParams true "包装单信息"
// @Success 200 {object} v1.Response
// @Router /wms/packs [post]
func (h *WmsPackHandler) Create(ctx *gin.Context) {
	var req v1.WmsPackCreateParams
	if err := ctx.ShouldBindJSON(&req); err != nil {
		v1.HandleError(ctx, http.StatusBadRequest, v1.ErrBadRequest, nil)
		return
	}

	if err := h.wmsPackService.Create(ctx, &req); err != nil {
		v1.HandleError(ctx, http.StatusInternalServerError, err, nil)
		return
	}

	v1.HandleSuccess(ctx, nil)
}

// Update godoc
// @Summary 更新包装单
// @Schemes
// @Description 更新指定ID的包装单信息
// @Tags 仓储模块,包装单管理
// @Accept json
// @Produce json
// @Security Bearer
// @Param id path int true "包装单ID"
// @Param request body v1.WmsPackUpdateParams true "包装单信息"
// @Success 200 {object} v1.Response
// @Router /wms/packs/{id} [patch]
func (h *WmsPackHandler) Update(ctx *gin.Context) {
	idStr := ctx.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		v1.HandleError(ctx, http.StatusBadRequest, v1.ErrBadRequest, nil)
		return
	}

	var req v1.WmsPackUpdateParams
	if err := ctx.ShouldBindJSON(&req); err != nil {
		v1.HandleError(ctx, http.StatusBadRequest, v1.ErrBadRequest, nil)
		return
	}

	if err := h.wmsPackService.Update(ctx, uint(id), &req); err != nil {
		v1.HandleError(ctx, http.StatusInternalServerError, err, nil)
		return
	}

	v1.HandleSuccess(ctx, nil)
}

// Delete godoc
// @Summary 删除包装单
// @Schemes
// @Description 删除指定ID的包装单
// @Tags 仓储模块,包装单管理
// @Accept json
// @Produce json
// @Security Bearer
// @Param id path int true "包装单ID"
// @Success 200 {object} v1.Response
// @Router /wms/packs/{id} [delete]
func (h *WmsPackHandler) Delete(ctx *gin.Context) {
	idStr := ctx.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		v1.HandleError(ctx, http.StatusBadRequest, v1.ErrBadRequest, nil)
		return
	}

	if err := h.wmsPackService.Delete(ctx, uint(id)); err != nil {
		v1.HandleError(ctx, http.StatusInternalServerError, err, nil)
		return
	}

	v1.HandleSuccess(ctx, nil)
}

// BatchDelete godoc
// @Summary 批量删除包装单
// @Schemes
// @Description 批量删除指定IDs的包装单
// @Tags 仓储模块,包装单管理
// @Accept json
// @Produce json
// @Security Bearer
// @Param request body v1.BatchDeleteParams true "包装单IDs"
// @Success 200 {object} v1.Response
// @Router /wms/packs [delete]
func (h *WmsPackHandler) BatchDelete(ctx *gin.Context) {
	var req v1.BatchDeleteParams
	if err := ctx.ShouldBindJSON(&req); err != nil {
		v1.HandleError(ctx, http.StatusBadRequest, v1.ErrBadRequest, nil)
		return
	}

	if err := h.wmsPackService.BatchDelete(ctx, req.IDs); err != nil {
		v1.HandleError(ctx, http.StatusInternalServerError, err, nil)
		return
	}

	v1.HandleSuccess(ctx, nil)
}

// Get godoc
// @Summary 获取包装单
// @Schemes
// @Description 获取指定ID的包装单信息
// @Tags 仓储模块,包装单管理
// @Accept json
// @Produce json
// @Security Bearer
// @Param id path int true "包装单ID"
// @Param _expand query string false "展开关联信息，支持: deliver,item,sku" example:"deliver,item,sku"
// @Success 200 {object} v1.Response{data=v1.WmsPackResponse}
// @Router /wms/packs/{id} [get]
func (h *WmsPackHandler) Get(ctx *gin.Context) {
	idStr := ctx.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		v1.HandleError(ctx, http.StatusBadRequest, v1.ErrBadRequest, nil)
		return
	}

	pack, err := h.wmsPackService.Get(ctx, uint(id), strings.Split(ctx.Query("_expand"), ","))
	if err != nil {
		v1.HandleError(ctx, http.StatusInternalServerError, err, nil)
		return
	}

	v1.HandleSuccess(ctx, pack)
}

// List godoc
// @Summary 获取包装单列表
// @Schemes
// @Description 分页获取包装单列表
// @Tags 仓储模块,包装单管理
// @Accept json
// @Produce json
// @Security Bearer
// @Param _page query int false "当前页码(默认值: 1)" example:"1"
// @Param _limit query int false "每页数量(默认值: 10)" example:"10"
// @Param _sort query string false "排序字段，多个字段用逗号分隔" example:"id"
// @Param _order query string false "排序方式，asc或desc，多个方式用逗号分隔" example:"desc"
// @Param q query string false "全文搜索" example:"关键词"
// @Param code query string false "编号筛选" example:"PACK202312010001"
// @Param deliverId query int false "出库单ID筛选" example:"1"
// @Param itemId query int false "物料ID筛选" example:"1"
// @Param skuId query int false "规格ID筛选" example:"1"
// @Param _expand query string false "展开关联信息，支持: deliver,item,sku" example:"deliver,item,sku"
// @Success 200 {object} v1.Response{data=pagination.Result{records=[]v1.WmsPackResponse}}
// @Router /wms/packs [get]
func (h *WmsPackHandler) List(ctx *gin.Context) {
	var params pagination.Params
	if err := ctx.ShouldBindQuery(&params); err != nil {
		v1.HandleError(ctx, http.StatusBadRequest, v1.ErrBadRequest, nil)
		return
	}

	// 编号筛选
	if code := ctx.DefaultQuery("code", ""); code != "" {
		params.AddFilter("code_like", code)
	}

	// 出库单ID筛选
	if deliverId := ctx.DefaultQuery("deliverId", ""); deliverId != "" {
		params.AddFilter("deliver_id", deliverId)
	}

	// 物料ID筛选
	if itemId := ctx.DefaultQuery("itemId", ""); itemId != "" {
		params.AddFilter("item_id", itemId)
	}

	// 规格ID筛选
	if skuId := ctx.DefaultQuery("skuId", ""); skuId != "" {
		params.AddFilter("sku_id", skuId)
	}

	result, err := h.wmsPackService.List(ctx, &params, strings.Split(ctx.Query("_expand"), ","))
	if err != nil {
		v1.HandleError(ctx, http.StatusInternalServerError, err, nil)
		return
	}

	v1.HandleSuccess(ctx, result)
}
