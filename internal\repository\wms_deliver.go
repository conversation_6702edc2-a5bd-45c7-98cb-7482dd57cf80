package repository

import (
	"context"
	v1 "daisy-server/api/v1"
	"daisy-server/internal/model"
	"daisy-server/pkg/pagination"
	"errors"

	"gorm.io/gorm"
)

type WmsDeliverRepository interface {
	Create(ctx context.Context, deliver *model.WmsDeliver) error
	Update(ctx context.Context, deliver *model.WmsDeliver) error
	Delete(ctx context.Context, id uint) error
	BatchDelete(ctx context.Context, ids []uint) error
	Get(ctx context.Context, id uint) (*model.WmsDeliver, error)
	GetByIds(ctx context.Context, ids []uint) ([]*model.WmsDeliver, error)
	List(ctx context.Context, params *pagination.Params) ([]*model.WmsDeliver, int64, error)
}

func NewWmsDeliverRepository(
	repository *Repository,
) WmsDeliverRepository {
	return &wmsDeliverRepository{
		Repository: repository,
	}
}

type wmsDeliverRepository struct {
	*Repository
}

func (r *wmsDeliverRepository) Create(ctx context.Context, deliver *model.WmsDeliver) error {
	if err := r.DB(ctx).Create(deliver).Error; err != nil {
		return err
	}
	return nil
}

func (r *wmsDeliverRepository) Update(ctx context.Context, deliver *model.WmsDeliver) error {
	if err := r.DB(ctx).Save(deliver).Error; err != nil {
		return err
	}
	return nil
}

func (r *wmsDeliverRepository) Delete(ctx context.Context, id uint) error {
	if err := r.DB(ctx).Delete(&model.WmsDeliver{}, id).Error; err != nil {
		return err
	}
	return nil
}

func (r *wmsDeliverRepository) BatchDelete(ctx context.Context, ids []uint) error {
	if err := r.DB(ctx).Delete(&model.WmsDeliver{}, ids).Error; err != nil {
		return err
	}
	return nil
}

func (r *wmsDeliverRepository) Get(ctx context.Context, id uint) (*model.WmsDeliver, error) {
	var deliver model.WmsDeliver
	if err := r.DB(ctx).First(&deliver, id).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, v1.ErrNotFound
		}
		return nil, err
	}
	return &deliver, nil
}

func (r *wmsDeliverRepository) GetByIds(ctx context.Context, ids []uint) ([]*model.WmsDeliver, error) {
	var delivers []*model.WmsDeliver
	if len(ids) == 0 {
		return delivers, nil
	}
	
	if err := r.DB(ctx).Where("id IN ?", ids).Find(&delivers).Error; err != nil {
		return nil, err
	}
	return delivers, nil
}

func (r *wmsDeliverRepository) List(ctx context.Context, params *pagination.Params) ([]*model.WmsDeliver, int64, error) {
	var records []*model.WmsDeliver
	var total int64

	db := r.DB(ctx).Model(&model.WmsDeliver{})

	// 使用GetResult方法直接获取分页结果
	total, err := params.GetResult(db, &records, &model.WmsDeliver{})
	if err != nil {
		return nil, 0, err
	}

	return records, total, nil
}
