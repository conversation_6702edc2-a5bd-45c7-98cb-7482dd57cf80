package handler

import (
	v1 "daisy-server/api/v1"
	"daisy-server/internal/service"
	"daisy-server/pkg/pagination"
	"net/http"
	"strconv"
	"strings"

	"github.com/gin-gonic/gin"
)

type WmsTransferSkuHandler struct {
	*Handler
	wmsTransferSkuService service.WmsTransferSkuService
}

func NewWmsTransferSkuHandler(
	handler *Handler,
	wmsTransferSkuService service.WmsTransferSkuService,
) *WmsTransferSkuHandler {
	return &WmsTransferSkuHandler{
		Handler:               handler,
		wmsTransferSkuService: wmsTransferSkuService,
	}
}

// Create godoc
// @Summary 创建调拨单物料规格
// @Schemes
// @Description 创建新的调拨单物料规格记录
// @Tags 仓储模块,调拨单物料规格管理
// @Accept json
// @Produce json
// @Security Bearer
// @Param request body v1.WmsTransferSkuCreateParams true "调拨单物料规格信息"
// @Success 200 {object} v1.Response
// @Router /wms/transfer-skus [post]
func (h *WmsTransferSkuHandler) Create(ctx *gin.Context) {
	var req v1.WmsTransferSkuCreateParams
	if err := ctx.ShouldBindJSON(&req); err != nil {
		v1.HandleError(ctx, http.StatusBadRequest, v1.ErrBadRequest, nil)
		return
	}

	if err := h.wmsTransferSkuService.Create(ctx, &req); err != nil {
		v1.HandleError(ctx, http.StatusInternalServerError, err, nil)
		return
	}

	v1.HandleSuccess(ctx, nil)
}

// Update godoc
// @Summary 更新调拨单物料规格
// @Schemes
// @Description 更新指定ID的调拨单物料规格信息
// @Tags 仓储模块,调拨单物料规格管理
// @Accept json
// @Produce json
// @Security Bearer
// @Param id path int true "调拨单物料规格ID"
// @Param request body v1.WmsTransferSkuUpdateParams true "调拨单物料规格信息"
// @Success 200 {object} v1.Response
// @Router /wms/transfer-skus/{id} [patch]
func (h *WmsTransferSkuHandler) Update(ctx *gin.Context) {
	idStr := ctx.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		v1.HandleError(ctx, http.StatusBadRequest, v1.ErrBadRequest, nil)
		return
	}

	var req v1.WmsTransferSkuUpdateParams
	if err := ctx.ShouldBindJSON(&req); err != nil {
		v1.HandleError(ctx, http.StatusBadRequest, v1.ErrBadRequest, nil)
		return
	}

	if err := h.wmsTransferSkuService.Update(ctx, uint(id), &req); err != nil {
		v1.HandleError(ctx, http.StatusInternalServerError, err, nil)
		return
	}

	v1.HandleSuccess(ctx, nil)
}

// Delete godoc
// @Summary 删除调拨单物料规格
// @Schemes
// @Description 删除指定ID的调拨单物料规格
// @Tags 仓储模块,调拨单物料规格管理
// @Accept json
// @Produce json
// @Security Bearer
// @Param id path int true "调拨单物料规格ID"
// @Success 200 {object} v1.Response
// @Router /wms/transfer-skus/{id} [delete]
func (h *WmsTransferSkuHandler) Delete(ctx *gin.Context) {
	idStr := ctx.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		v1.HandleError(ctx, http.StatusBadRequest, v1.ErrBadRequest, nil)
		return
	}

	if err := h.wmsTransferSkuService.Delete(ctx, uint(id)); err != nil {
		v1.HandleError(ctx, http.StatusInternalServerError, err, nil)
		return
	}

	v1.HandleSuccess(ctx, nil)
}

// BatchDelete godoc
// @Summary 批量删除调拨单物料规格
// @Schemes
// @Description 批量删除指定IDs的调拨单物料规格
// @Tags 仓储模块,调拨单物料规格管理
// @Accept json
// @Produce json
// @Security Bearer
// @Param request body v1.BatchDeleteParams true "调拨单物料规格IDs"
// @Success 200 {object} v1.Response
// @Router /wms/transfer-skus [delete]
func (h *WmsTransferSkuHandler) BatchDelete(ctx *gin.Context) {
	var req v1.BatchDeleteParams
	if err := ctx.ShouldBindJSON(&req); err != nil {
		v1.HandleError(ctx, http.StatusBadRequest, v1.ErrBadRequest, nil)
		return
	}

	if err := h.wmsTransferSkuService.BatchDelete(ctx, req.IDs); err != nil {
		v1.HandleError(ctx, http.StatusInternalServerError, err, nil)
		return
	}

	v1.HandleSuccess(ctx, nil)
}

// Get godoc
// @Summary 获取调拨单物料规格
// @Schemes
// @Description 获取指定ID的调拨单物料规格信息
// @Tags 仓储模块,调拨单物料规格管理
// @Accept json
// @Produce json
// @Security Bearer
// @Param id path int true "调拨单物料规格ID"
// @Param _expand query string false "展开关联信息，支持: transfer,item,sku" example:"transfer,item,sku"
// @Success 200 {object} v1.Response{data=v1.WmsTransferSkuResponse}
// @Router /wms/transfer-skus/{id} [get]
func (h *WmsTransferSkuHandler) Get(ctx *gin.Context) {
	idStr := ctx.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		v1.HandleError(ctx, http.StatusBadRequest, v1.ErrBadRequest, nil)
		return
	}

	transferSku, err := h.wmsTransferSkuService.Get(ctx, uint(id), strings.Split(ctx.Query("_expand"), ","))
	if err != nil {
		v1.HandleError(ctx, http.StatusInternalServerError, err, nil)
		return
	}

	v1.HandleSuccess(ctx, transferSku)
}

// List godoc
// @Summary 获取调拨单物料规格列表
// @Schemes
// @Description 分页获取调拨单物料规格列表
// @Tags 仓储模块,调拨单物料规格管理
// @Accept json
// @Produce json
// @Security Bearer
// @Param _page query int false "当前页码(默认值: 1)" example:"1"
// @Param _limit query int false "每页数量(默认值: 10)" example:"10"
// @Param _sort query string false "排序字段，多个字段用逗号分隔" example:"id"
// @Param _order query string false "排序方式，asc或desc，多个方式用逗号分隔" example:"desc"
// @Param q query string false "全文搜索" example:"关键词"
// @Param status query bool false "状态筛选" example:"true"
// @Param transferId query int false "调拨单ID筛选" example:"1"
// @Param itemId query int false "物料ID筛选" example:"1"
// @Param skuId query int false "规格ID筛选" example:"1"
// @Param _expand query string false "展开关联信息，支持: transfer,item,sku" example:"transfer,item,sku"
// @Success 200 {object} v1.Response{data=pagination.Result{records=[]v1.WmsTransferSkuResponse}}
// @Router /wms/transfer-skus [get]
func (h *WmsTransferSkuHandler) List(ctx *gin.Context) {
	var params pagination.Params
	if err := ctx.ShouldBindQuery(&params); err != nil {
		v1.HandleError(ctx, http.StatusBadRequest, v1.ErrBadRequest, nil)
		return
	}

	// 状态筛选
	if status := ctx.DefaultQuery("status", ""); status != "" {
		params.AddFilter("status", status)
	}

	// 调拨单ID筛选
	if transferId := ctx.DefaultQuery("transferId", ""); transferId != "" {
		params.AddFilter("transfer_id", transferId)
	}

	// 物料ID筛选
	if itemId := ctx.DefaultQuery("itemId", ""); itemId != "" {
		params.AddFilter("item_id", itemId)
	}

	// 规格ID筛选
	if skuId := ctx.DefaultQuery("skuId", ""); skuId != "" {
		params.AddFilter("sku_id", skuId)
	}

	result, err := h.wmsTransferSkuService.List(ctx, &params, strings.Split(ctx.Query("_expand"), ","))
	if err != nil {
		v1.HandleError(ctx, http.StatusInternalServerError, err, nil)
		return
	}

	v1.HandleSuccess(ctx, result)
}
