package repository

import (
	"context"
	v1 "daisy-server/api/v1"
	"daisy-server/internal/model"
	"daisy-server/pkg/pagination"
	"errors"

	"gorm.io/gorm"
)

type WmsReceiveSkuRepository interface {
	Create(ctx context.Context, receiveSku *model.WmsReceiveSku) error
	Update(ctx context.Context, receiveSku *model.WmsReceiveSku) error
	Delete(ctx context.Context, id uint) error
	BatchDelete(ctx context.Context, ids []uint) error
	Get(ctx context.Context, id uint) (*model.WmsReceiveSku, error)
	GetByIds(ctx context.Context, ids []uint) ([]*model.WmsReceiveSku, error)
	List(ctx context.Context, params *pagination.Params) ([]*model.WmsReceiveSku, int64, error)
}

func NewWmsReceiveSkuRepository(
	repository *Repository,
) WmsReceiveSkuRepository {
	return &wmsReceiveSkuRepository{
		Repository: repository,
	}
}

type wmsReceiveSkuRepository struct {
	*Repository
}

func (r *wmsReceiveSkuRepository) Create(ctx context.Context, receiveSku *model.WmsReceiveSku) error {
	if err := r.DB(ctx).Create(receiveSku).Error; err != nil {
		return err
	}
	return nil
}

func (r *wmsReceiveSkuRepository) Update(ctx context.Context, receiveSku *model.WmsReceiveSku) error {
	if err := r.DB(ctx).Save(receiveSku).Error; err != nil {
		return err
	}
	return nil
}

func (r *wmsReceiveSkuRepository) Delete(ctx context.Context, id uint) error {
	if err := r.DB(ctx).Delete(&model.WmsReceiveSku{}, id).Error; err != nil {
		return err
	}
	return nil
}

func (r *wmsReceiveSkuRepository) BatchDelete(ctx context.Context, ids []uint) error {
	if err := r.DB(ctx).Delete(&model.WmsReceiveSku{}, ids).Error; err != nil {
		return err
	}
	return nil
}

func (r *wmsReceiveSkuRepository) Get(ctx context.Context, id uint) (*model.WmsReceiveSku, error) {
	var receiveSku model.WmsReceiveSku
	if err := r.DB(ctx).First(&receiveSku, id).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, v1.ErrNotFound
		}
		return nil, err
	}
	return &receiveSku, nil
}

func (r *wmsReceiveSkuRepository) GetByIds(ctx context.Context, ids []uint) ([]*model.WmsReceiveSku, error) {
	var receiveSkus []*model.WmsReceiveSku
	if len(ids) == 0 {
		return receiveSkus, nil
	}
	
	if err := r.DB(ctx).Where("id IN ?", ids).Find(&receiveSkus).Error; err != nil {
		return nil, err
	}
	return receiveSkus, nil
}

func (r *wmsReceiveSkuRepository) List(ctx context.Context, params *pagination.Params) ([]*model.WmsReceiveSku, int64, error) {
	var records []*model.WmsReceiveSku
	var total int64

	db := r.DB(ctx).Model(&model.WmsReceiveSku{})

	// 使用GetResult方法直接获取分页结果
	total, err := params.GetResult(db, &records, &model.WmsReceiveSku{})
	if err != nil {
		return nil, 0, err
	}

	return records, total, nil
}
