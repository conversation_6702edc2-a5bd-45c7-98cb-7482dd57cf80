package handler

import (
	v1 "daisy-server/api/v1"
	"daisy-server/internal/service"
	"daisy-server/pkg/pagination"
	"net/http"
	"strconv"
	"strings"

	"github.com/gin-gonic/gin"
)

type WmsDeliverHandler struct {
	*Handler
	wmsDeliverService service.WmsDeliverService
}

func NewWmsDeliverHandler(
	handler *Handler,
	wmsDeliverService service.WmsDeliverService,
) *WmsDeliverHandler {
	return &WmsDeliverHandler{
		Handler:           handler,
		wmsDeliverService: wmsDeliverService,
	}
}

// Create godoc
// @Summary 创建出库单
// @Schemes
// @Description 创建新的出库单记录
// @Tags 仓储模块,出库单管理
// @Accept json
// @Produce json
// @Security Bearer
// @Param request body v1.WmsDeliverCreateParams true "出库单信息"
// @Success 200 {object} v1.Response
// @Router /wms/delivers [post]
func (h *WmsDeliverHandler) Create(ctx *gin.Context) {
	var req v1.WmsDeliverCreateParams
	if err := ctx.ShouldBindJSON(&req); err != nil {
		v1.HandleError(ctx, http.StatusBadRequest, v1.ErrBadRequest, nil)
		return
	}

	if err := h.wmsDeliverService.Create(ctx, &req); err != nil {
		v1.HandleError(ctx, http.StatusInternalServerError, err, nil)
		return
	}

	v1.HandleSuccess(ctx, nil)
}

// Update godoc
// @Summary 更新出库单
// @Schemes
// @Description 更新指定ID的出库单信息
// @Tags 仓储模块,出库单管理
// @Accept json
// @Produce json
// @Security Bearer
// @Param id path int true "出库单ID"
// @Param request body v1.WmsDeliverUpdateParams true "出库单信息"
// @Success 200 {object} v1.Response
// @Router /wms/delivers/{id} [patch]
func (h *WmsDeliverHandler) Update(ctx *gin.Context) {
	idStr := ctx.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		v1.HandleError(ctx, http.StatusBadRequest, v1.ErrBadRequest, nil)
		return
	}

	var req v1.WmsDeliverUpdateParams
	if err := ctx.ShouldBindJSON(&req); err != nil {
		v1.HandleError(ctx, http.StatusBadRequest, v1.ErrBadRequest, nil)
		return
	}

	if err := h.wmsDeliverService.Update(ctx, uint(id), &req); err != nil {
		v1.HandleError(ctx, http.StatusInternalServerError, err, nil)
		return
	}

	v1.HandleSuccess(ctx, nil)
}

// Delete godoc
// @Summary 删除出库单
// @Schemes
// @Description 删除指定ID的出库单
// @Tags 仓储模块,出库单管理
// @Accept json
// @Produce json
// @Security Bearer
// @Param id path int true "出库单ID"
// @Success 200 {object} v1.Response
// @Router /wms/delivers/{id} [delete]
func (h *WmsDeliverHandler) Delete(ctx *gin.Context) {
	idStr := ctx.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		v1.HandleError(ctx, http.StatusBadRequest, v1.ErrBadRequest, nil)
		return
	}

	if err := h.wmsDeliverService.Delete(ctx, uint(id)); err != nil {
		v1.HandleError(ctx, http.StatusInternalServerError, err, nil)
		return
	}

	v1.HandleSuccess(ctx, nil)
}

// BatchDelete godoc
// @Summary 批量删除出库单
// @Schemes
// @Description 批量删除指定IDs的出库单
// @Tags 仓储模块,出库单管理
// @Accept json
// @Produce json
// @Security Bearer
// @Param request body v1.BatchDeleteParams true "出库单IDs"
// @Success 200 {object} v1.Response
// @Router /wms/delivers [delete]
func (h *WmsDeliverHandler) BatchDelete(ctx *gin.Context) {
	var req v1.BatchDeleteParams
	if err := ctx.ShouldBindJSON(&req); err != nil {
		v1.HandleError(ctx, http.StatusBadRequest, v1.ErrBadRequest, nil)
		return
	}

	if err := h.wmsDeliverService.BatchDelete(ctx, req.IDs); err != nil {
		v1.HandleError(ctx, http.StatusInternalServerError, err, nil)
		return
	}

	v1.HandleSuccess(ctx, nil)
}

// Get godoc
// @Summary 获取出库单
// @Schemes
// @Description 获取指定ID的出库单信息
// @Tags 仓储模块,出库单管理
// @Accept json
// @Produce json
// @Security Bearer
// @Param id path int true "出库单ID"
// @Param _expand query string false "展开关联信息，支持: partner" example:"partner"
// @Success 200 {object} v1.Response{data=v1.WmsDeliverResponse}
// @Router /wms/delivers/{id} [get]
func (h *WmsDeliverHandler) Get(ctx *gin.Context) {
	idStr := ctx.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		v1.HandleError(ctx, http.StatusBadRequest, v1.ErrBadRequest, nil)
		return
	}

	deliver, err := h.wmsDeliverService.Get(ctx, uint(id), strings.Split(ctx.Query("_expand"), ","))
	if err != nil {
		v1.HandleError(ctx, http.StatusInternalServerError, err, nil)
		return
	}

	v1.HandleSuccess(ctx, deliver)
}

// List godoc
// @Summary 获取出库单列表
// @Schemes
// @Description 分页获取出库单列表
// @Tags 仓储模块,出库单管理
// @Accept json
// @Produce json
// @Security Bearer
// @Param _page query int false "当前页码(默认值: 1)" example:"1"
// @Param _limit query int false "每页数量(默认值: 10)" example:"10"
// @Param _sort query string false "排序字段，多个字段用逗号分隔" example:"id"
// @Param _order query string false "排序方式，asc或desc，多个方式用逗号分隔" example:"desc"
// @Param q query string false "全文搜索" example:"关键词"
// @Param status query int false "状态筛选" example:"1"
// @Param code query string false "编号筛选" example:"DEL202312010001"
// @Param type query int false "类型筛选" example:"1"
// @Param partnerId query int false "客户ID筛选" example:"1"
// @Param relatedNo query string false "关联编号筛选" example:"SO202312010001"
// @Param _expand query string false "展开关联信息，支持: partner" example:"partner"
// @Success 200 {object} v1.Response{data=pagination.Result{records=[]v1.WmsDeliverResponse}}
// @Router /wms/delivers [get]
func (h *WmsDeliverHandler) List(ctx *gin.Context) {
	var params pagination.Params
	if err := ctx.ShouldBindQuery(&params); err != nil {
		v1.HandleError(ctx, http.StatusBadRequest, v1.ErrBadRequest, nil)
		return
	}

	// 状态筛选
	if status := ctx.DefaultQuery("status", ""); status != "" {
		params.AddFilter("status", status)
	}

	// 编号筛选
	if code := ctx.DefaultQuery("code", ""); code != "" {
		params.AddFilter("code_like", code)
	}

	// 类型筛选
	if deliverType := ctx.DefaultQuery("type", ""); deliverType != "" {
		params.AddFilter("type", deliverType)
	}

	// 客户ID筛选
	if partnerId := ctx.DefaultQuery("partnerId", ""); partnerId != "" {
		params.AddFilter("partner_id", partnerId)
	}

	// 关联编号筛选
	if relatedNo := ctx.DefaultQuery("relatedNo", ""); relatedNo != "" {
		params.AddFilter("related_no_like", relatedNo)
	}

	result, err := h.wmsDeliverService.List(ctx, &params, strings.Split(ctx.Query("_expand"), ","))
	if err != nil {
		v1.HandleError(ctx, http.StatusInternalServerError, err, nil)
		return
	}

	v1.HandleSuccess(ctx, result)
}
