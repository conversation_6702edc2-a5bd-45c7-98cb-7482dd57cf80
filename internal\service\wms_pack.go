package service

import (
	"context"
	v1 "daisy-server/api/v1"
	"daisy-server/internal/model"
	"daisy-server/internal/repository"
	"daisy-server/pkg/pagination"
	"slices"
	"time"

	"github.com/jinzhu/copier"
)

type WmsPackService interface {
	Create(ctx context.Context, req *v1.WmsPackCreateParams) error
	Update(ctx context.Context, id uint, req *v1.WmsPackUpdateParams) error
	Delete(ctx context.Context, id uint) error
	BatchDelete(ctx context.Context, ids []uint) error
	Get(ctx context.Context, id uint, expand []string) (*v1.WmsPackResponse, error)
	List(ctx context.Context, params *pagination.Params, expand []string) (*pagination.Result, error)
}

func NewWmsPackService(
	service *Service,
	wmsPackRepository repository.WmsPackRepository,
	wmsDeliverRepository repository.WmsDeliverRepository,
	wmsItemRepository repository.WmsItemRepository,
	wmsSkuRepository repository.WmsSkuRepository,
) WmsPackService {
	return &wmsPackService{
		Service:              service,
		wmsPackRepository:    wmsPackRepository,
		wmsDeliverRepository: wmsDeliverRepository,
		wmsItemRepository:    wmsItemRepository,
		wmsSkuRepository:     wmsSkuRepository,
	}
}

type wmsPackService struct {
	*Service
	wmsPackRepository    repository.WmsPackRepository
	wmsDeliverRepository repository.WmsDeliverRepository
	wmsItemRepository    repository.WmsItemRepository
	wmsSkuRepository     repository.WmsSkuRepository
}

// 包装单相关方法实现
func (s *wmsPackService) Create(ctx context.Context, req *v1.WmsPackCreateParams) error {
	pack := &model.WmsPack{}
	if err := copier.Copy(pack, req); err != nil {
		return err
	}

	// 获取当前用户信息
	user := ctx.Value("user").(*model.SysUser)

	// 租户ID
	pack.TenantId = user.TenantId

	// 创建人
	if user.Nickname != "" {
		pack.CreatedBy = user.Nickname
	} else {
		pack.CreatedBy = user.Username
	}

	return s.tm.Transaction(ctx, func(ctx context.Context) error {
		if err := s.wmsPackRepository.Create(ctx, pack); err != nil {
			return err
		}
		return nil
	})
}

func (s *wmsPackService) Update(ctx context.Context, id uint, req *v1.WmsPackUpdateParams) error {
	pack, err := s.wmsPackRepository.Get(ctx, id)
	if err != nil {
		return err
	}

	// 获取当前用户信息
	user := ctx.Value("user").(*model.SysUser)

	// 过滤非本租户数据
	if pack.TenantId != user.TenantId {
		return v1.ErrForbidden
	}

	if err := copier.Copy(pack, req); err != nil {
		return err
	}

	// 更新人
	if user.Nickname != "" {
		pack.UpdatedBy = user.Nickname
	} else {
		pack.UpdatedBy = user.Username
	}

	return s.tm.Transaction(ctx, func(ctx context.Context) error {
		if err := s.wmsPackRepository.Update(ctx, pack); err != nil {
			return err
		}
		return nil
	})
}

func (s *wmsPackService) Delete(ctx context.Context, id uint) error {
	// 获取当前用户信息
	user := ctx.Value("user").(*model.SysUser)

	// 是否是该租户数据
	pack, err := s.wmsPackRepository.Get(ctx, id)
	if err != nil {
		return err
	}
	if pack.TenantId != user.TenantId {
		return v1.ErrForbidden
	}

	return s.tm.Transaction(ctx, func(ctx context.Context) error {
		if err := s.wmsPackRepository.Delete(ctx, id); err != nil {
			return err
		}
		return nil
	})
}

func (s *wmsPackService) BatchDelete(ctx context.Context, ids []uint) error {
	// 获取当前用户信息
	user := ctx.Value("user").(*model.SysUser)

	// 获取目标记录
	packs, err := s.wmsPackRepository.GetByIds(ctx, ids)
	if err != nil {
		return err
	}

	// 过滤非本租户数据
	var newIds []uint
	for _, pack := range packs {
		if pack.TenantId == user.TenantId {
			newIds = append(newIds, pack.ID)
		}
	}

	return s.tm.Transaction(ctx, func(ctx context.Context) error {
		if err := s.wmsPackRepository.BatchDelete(ctx, newIds); err != nil {
			return err
		}
		return nil
	})
}

func (s *wmsPackService) Get(ctx context.Context, id uint, expand []string) (*v1.WmsPackResponse, error) {
	pack, err := s.wmsPackRepository.Get(ctx, id)
	if err != nil {
		return nil, err
	}

	// 获取当前用户信息
	user := ctx.Value("user").(*model.SysUser)

	// 过滤非本租户数据
	if pack.TenantId != user.TenantId {
		return nil, v1.ErrForbidden
	}

	response := &v1.WmsPackResponse{}
	if err := copier.Copy(response, pack); err != nil {
		return nil, err
	}

	response.CreatedAt = pack.CreatedAt.Format(time.RFC3339)
	response.UpdatedAt = pack.UpdatedAt.Format(time.RFC3339)

	// 展开出库单信息
	if slices.Contains(expand, "deliver") && pack.DeliverId > 0 {
		deliver, err := s.wmsDeliverRepository.Get(ctx, pack.DeliverId)
		if err == nil {
			deliverResponse := &v1.WmsDeliverResponse{}
			if err := copier.Copy(deliverResponse, deliver); err == nil {
				deliverResponse.CreatedAt = deliver.CreatedAt.Format(time.RFC3339)
				deliverResponse.UpdatedAt = deliver.UpdatedAt.Format(time.RFC3339)
				response.Deliver = deliverResponse
			}
		}
	}

	// 展开物料信息
	if slices.Contains(expand, "item") && pack.ItemId > 0 {
		item, err := s.wmsItemRepository.Get(ctx, pack.ItemId)
		if err == nil {
			itemResponse := &v1.WmsItemResponse{}
			if err := copier.Copy(itemResponse, item); err == nil {
				itemResponse.CreatedAt = item.CreatedAt.Format(time.RFC3339)
				itemResponse.UpdatedAt = item.UpdatedAt.Format(time.RFC3339)
				response.Item = itemResponse
			}
		}
	}

	// 展开规格信息
	if slices.Contains(expand, "sku") && pack.SkuId > 0 {
		sku, err := s.wmsSkuRepository.Get(ctx, pack.SkuId)
		if err == nil {
			skuResponse := &v1.WmsSkuResponse{}
			if err := copier.Copy(skuResponse, sku); err == nil {
				skuResponse.CreatedAt = sku.CreatedAt.Format(time.RFC3339)
				skuResponse.UpdatedAt = sku.UpdatedAt.Format(time.RFC3339)
				response.Sku = skuResponse
			}
		}
	}

	return response, nil
}

func (s *wmsPackService) List(ctx context.Context, params *pagination.Params, expand []string) (*pagination.Result, error) {
	// 获取当前用户信息
	user := ctx.Value("user").(*model.SysUser)

	// 过滤非本租户数据
	params.AddFilter("tenant_id", user.TenantId)

	packs, total, err := s.wmsPackRepository.List(ctx, params)
	if err != nil {
		return nil, err
	}

	// 将原始记录转换为API响应格式
	records := make([]*v1.WmsPackResponse, 0, len(packs))
	for _, pack := range packs {
		response := &v1.WmsPackResponse{}
		if err := copier.Copy(response, pack); err != nil {
			return nil, err
		}

		response.CreatedAt = pack.CreatedAt.Format(time.RFC3339)
		response.UpdatedAt = pack.UpdatedAt.Format(time.RFC3339)

		// 展开出库单信息
		if slices.Contains(expand, "deliver") && pack.DeliverId > 0 {
			deliver, err := s.wmsDeliverRepository.Get(ctx, pack.DeliverId)
			if err == nil {
				deliverResponse := &v1.WmsDeliverResponse{}
				if err := copier.Copy(deliverResponse, deliver); err == nil {
					deliverResponse.CreatedAt = deliver.CreatedAt.Format(time.RFC3339)
					deliverResponse.UpdatedAt = deliver.UpdatedAt.Format(time.RFC3339)
					response.Deliver = deliverResponse
				}
			}
		}

		// 展开物料信息
		if slices.Contains(expand, "item") && pack.ItemId > 0 {
			item, err := s.wmsItemRepository.Get(ctx, pack.ItemId)
			if err == nil {
				itemResponse := &v1.WmsItemResponse{}
				if err := copier.Copy(itemResponse, item); err == nil {
					itemResponse.CreatedAt = item.CreatedAt.Format(time.RFC3339)
					itemResponse.UpdatedAt = item.UpdatedAt.Format(time.RFC3339)
					response.Item = itemResponse
				}
			}
		}

		// 展开规格信息
		if slices.Contains(expand, "sku") && pack.SkuId > 0 {
			sku, err := s.wmsSkuRepository.Get(ctx, pack.SkuId)
			if err == nil {
				skuResponse := &v1.WmsSkuResponse{}
				if err := copier.Copy(skuResponse, sku); err == nil {
					skuResponse.CreatedAt = sku.CreatedAt.Format(time.RFC3339)
					skuResponse.UpdatedAt = sku.UpdatedAt.Format(time.RFC3339)
					response.Sku = skuResponse
				}
			}
		}

		records = append(records, response)
	}

	return &pagination.Result{
		Records: records,
		Total:   total,
	}, nil
}
