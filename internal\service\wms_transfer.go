package service

import (
	"context"
	v1 "daisy-server/api/v1"
	"daisy-server/internal/model"
	"daisy-server/internal/repository"
	"daisy-server/pkg/pagination"
	"time"

	"github.com/jinzhu/copier"
)

type WmsTransferService interface {
	Create(ctx context.Context, req *v1.WmsTransferCreateParams) error
	Update(ctx context.Context, id uint, req *v1.WmsTransferUpdateParams) error
	Delete(ctx context.Context, id uint) error
	BatchDelete(ctx context.Context, ids []uint) error
	Get(ctx context.Context, id uint) (*v1.WmsTransferResponse, error)
	List(ctx context.Context, params *pagination.Params) (*pagination.Result, error)
}

func NewWmsTransferService(
	service *Service,
	wmsTransferRepository repository.WmsTransferRepository,
) WmsTransferService {
	return &wmsTransferService{
		Service:               service,
		wmsTransferRepository: wmsTransferRepository,
	}
}

type wmsTransferService struct {
	*Service
	wmsTransferRepository repository.WmsTransferRepository
}

// 调拨单相关方法实现
func (s *wmsTransferService) Create(ctx context.Context, req *v1.WmsTransferCreateParams) error {
	transfer := &model.WmsTransfer{}
	if err := copier.Copy(transfer, req); err != nil {
		return err
	}

	// 获取当前用户信息
	user := ctx.Value("user").(*model.SysUser)

	// 租户ID
	transfer.TenantId = user.TenantId

	// 创建人
	if user.Nickname != "" {
		transfer.CreatedBy = user.Nickname
	} else {
		transfer.CreatedBy = user.Username
	}

	return s.tm.Transaction(ctx, func(ctx context.Context) error {
		if err := s.wmsTransferRepository.Create(ctx, transfer); err != nil {
			return err
		}
		return nil
	})
}

func (s *wmsTransferService) Update(ctx context.Context, id uint, req *v1.WmsTransferUpdateParams) error {
	transfer, err := s.wmsTransferRepository.Get(ctx, id)
	if err != nil {
		return err
	}

	// 获取当前用户信息
	user := ctx.Value("user").(*model.SysUser)

	// 过滤非本租户数据
	if transfer.TenantId != user.TenantId {
		return v1.ErrForbidden
	}

	if err := copier.Copy(transfer, req); err != nil {
		return err
	}

	// 更新人
	if user.Nickname != "" {
		transfer.UpdatedBy = user.Nickname
	} else {
		transfer.UpdatedBy = user.Username
	}

	return s.tm.Transaction(ctx, func(ctx context.Context) error {
		if err := s.wmsTransferRepository.Update(ctx, transfer); err != nil {
			return err
		}
		return nil
	})
}

func (s *wmsTransferService) Delete(ctx context.Context, id uint) error {
	// 获取当前用户信息
	user := ctx.Value("user").(*model.SysUser)

	// 是否是该租户数据
	transfer, err := s.wmsTransferRepository.Get(ctx, id)
	if err != nil {
		return err
	}
	if transfer.TenantId != user.TenantId {
		return v1.ErrForbidden
	}

	return s.tm.Transaction(ctx, func(ctx context.Context) error {
		if err := s.wmsTransferRepository.Delete(ctx, id); err != nil {
			return err
		}
		return nil
	})
}

func (s *wmsTransferService) BatchDelete(ctx context.Context, ids []uint) error {
	// 获取当前用户信息
	user := ctx.Value("user").(*model.SysUser)

	// 获取目标记录
	transfers, err := s.wmsTransferRepository.GetByIds(ctx, ids)
	if err != nil {
		return err
	}

	// 过滤非本租户数据
	var newIds []uint
	for _, transfer := range transfers {
		if transfer.TenantId == user.TenantId {
			newIds = append(newIds, transfer.ID)
		}
	}

	return s.tm.Transaction(ctx, func(ctx context.Context) error {
		if err := s.wmsTransferRepository.BatchDelete(ctx, newIds); err != nil {
			return err
		}
		return nil
	})
}

func (s *wmsTransferService) Get(ctx context.Context, id uint) (*v1.WmsTransferResponse, error) {
	transfer, err := s.wmsTransferRepository.Get(ctx, id)
	if err != nil {
		return nil, err
	}

	// 获取当前用户信息
	user := ctx.Value("user").(*model.SysUser)

	// 过滤非本租户数据
	if transfer.TenantId != user.TenantId {
		return nil, v1.ErrForbidden
	}

	response := &v1.WmsTransferResponse{}
	if err := copier.Copy(response, transfer); err != nil {
		return nil, err
	}

	response.CreatedAt = transfer.CreatedAt.Format(time.RFC3339)
	response.UpdatedAt = transfer.UpdatedAt.Format(time.RFC3339)

	return response, nil
}

func (s *wmsTransferService) List(ctx context.Context, params *pagination.Params) (*pagination.Result, error) {
	// 获取当前用户信息
	user := ctx.Value("user").(*model.SysUser)

	// 过滤非本租户数据
	params.AddFilter("tenant_id", user.TenantId)

	transfers, total, err := s.wmsTransferRepository.List(ctx, params)
	if err != nil {
		return nil, err
	}

	// 将原始记录转换为API响应格式
	records := make([]*v1.WmsTransferResponse, 0, len(transfers))
	for _, transfer := range transfers {
		response := &v1.WmsTransferResponse{}
		if err := copier.Copy(response, transfer); err != nil {
			return nil, err
		}

		response.CreatedAt = transfer.CreatedAt.Format(time.RFC3339)
		response.UpdatedAt = transfer.UpdatedAt.Format(time.RFC3339)

		records = append(records, response)
	}

	return &pagination.Result{
		Records: records,
		Total:   total,
	}, nil
}
