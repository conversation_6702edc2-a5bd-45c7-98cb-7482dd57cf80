package service

import (
	"context"
	v1 "daisy-server/api/v1"
	"daisy-server/internal/model"
	"daisy-server/internal/repository"
	"daisy-server/pkg/pagination"
	"slices"
	"time"

	"github.com/jinzhu/copier"
)

type WmsDeliverService interface {
	Create(ctx context.Context, req *v1.WmsDeliverCreateParams) error
	Update(ctx context.Context, id uint, req *v1.WmsDeliverUpdateParams) error
	Delete(ctx context.Context, id uint) error
	BatchDelete(ctx context.Context, ids []uint) error
	Get(ctx context.Context, id uint, expand []string) (*v1.WmsDeliverResponse, error)
	List(ctx context.Context, params *pagination.Params, expand []string) (*pagination.Result, error)
}

func NewWmsDeliverService(
	service *Service,
	wmsDeliverRepository repository.WmsDeliverRepository,
	wmsPartnerRepository repository.WmsPartnerRepository,
) WmsDeliverService {
	return &wmsDeliverService{
		Service:              service,
		wmsDeliverRepository: wmsDeliverRepository,
		wmsPartnerRepository: wmsPartnerRepository,
	}
}

type wmsDeliverService struct {
	*Service
	wmsDeliverRepository repository.WmsDeliverRepository
	wmsPartnerRepository repository.WmsPartnerRepository
}

// 出库单相关方法实现
func (s *wmsDeliverService) Create(ctx context.Context, req *v1.WmsDeliverCreateParams) error {
	deliver := &model.WmsDeliver{}
	if err := copier.Copy(deliver, req); err != nil {
		return err
	}

	// 获取当前用户信息
	user := ctx.Value("user").(*model.SysUser)

	// 租户ID
	deliver.TenantId = user.TenantId

	// 创建人
	if user.Nickname != "" {
		deliver.CreatedBy = user.Nickname
	} else {
		deliver.CreatedBy = user.Username
	}

	return s.tm.Transaction(ctx, func(ctx context.Context) error {
		if err := s.wmsDeliverRepository.Create(ctx, deliver); err != nil {
			return err
		}
		return nil
	})
}

func (s *wmsDeliverService) Update(ctx context.Context, id uint, req *v1.WmsDeliverUpdateParams) error {
	deliver, err := s.wmsDeliverRepository.Get(ctx, id)
	if err != nil {
		return err
	}

	// 获取当前用户信息
	user := ctx.Value("user").(*model.SysUser)

	// 过滤非本租户数据
	if deliver.TenantId != user.TenantId {
		return v1.ErrForbidden
	}

	if err := copier.Copy(deliver, req); err != nil {
		return err
	}

	// 更新人
	if user.Nickname != "" {
		deliver.UpdatedBy = user.Nickname
	} else {
		deliver.UpdatedBy = user.Username
	}

	return s.tm.Transaction(ctx, func(ctx context.Context) error {
		if err := s.wmsDeliverRepository.Update(ctx, deliver); err != nil {
			return err
		}
		return nil
	})
}

func (s *wmsDeliverService) Delete(ctx context.Context, id uint) error {
	// 获取当前用户信息
	user := ctx.Value("user").(*model.SysUser)

	// 是否是该租户数据
	deliver, err := s.wmsDeliverRepository.Get(ctx, id)
	if err != nil {
		return err
	}
	if deliver.TenantId != user.TenantId {
		return v1.ErrForbidden
	}

	return s.tm.Transaction(ctx, func(ctx context.Context) error {
		if err := s.wmsDeliverRepository.Delete(ctx, id); err != nil {
			return err
		}
		return nil
	})
}

func (s *wmsDeliverService) BatchDelete(ctx context.Context, ids []uint) error {
	// 获取当前用户信息
	user := ctx.Value("user").(*model.SysUser)

	// 获取目标记录
	delivers, err := s.wmsDeliverRepository.GetByIds(ctx, ids)
	if err != nil {
		return err
	}

	// 过滤非本租户数据
	var newIds []uint
	for _, deliver := range delivers {
		if deliver.TenantId == user.TenantId {
			newIds = append(newIds, deliver.ID)
		}
	}

	return s.tm.Transaction(ctx, func(ctx context.Context) error {
		if err := s.wmsDeliverRepository.BatchDelete(ctx, newIds); err != nil {
			return err
		}
		return nil
	})
}

func (s *wmsDeliverService) Get(ctx context.Context, id uint, expand []string) (*v1.WmsDeliverResponse, error) {
	deliver, err := s.wmsDeliverRepository.Get(ctx, id)
	if err != nil {
		return nil, err
	}

	// 获取当前用户信息
	user := ctx.Value("user").(*model.SysUser)

	// 过滤非本租户数据
	if deliver.TenantId != user.TenantId {
		return nil, v1.ErrForbidden
	}

	response := &v1.WmsDeliverResponse{}
	if err := copier.Copy(response, deliver); err != nil {
		return nil, err
	}

	response.CreatedAt = deliver.CreatedAt.Format(time.RFC3339)
	response.UpdatedAt = deliver.UpdatedAt.Format(time.RFC3339)

	// 展开客户信息
	if slices.Contains(expand, "partner") && deliver.PartnerId > 0 {
		partner, err := s.wmsPartnerRepository.Get(ctx, deliver.PartnerId)
		if err == nil {
			partnerResponse := &v1.WmsPartnerResponse{}
			if err := copier.Copy(partnerResponse, partner); err == nil {
				partnerResponse.CreatedAt = partner.CreatedAt.Format(time.RFC3339)
				partnerResponse.UpdatedAt = partner.UpdatedAt.Format(time.RFC3339)
				response.Partner = partnerResponse
			}
		}
	}

	return response, nil
}

func (s *wmsDeliverService) List(ctx context.Context, params *pagination.Params, expand []string) (*pagination.Result, error) {
	// 获取当前用户信息
	user := ctx.Value("user").(*model.SysUser)

	// 过滤非本租户数据
	params.AddFilter("tenant_id", user.TenantId)

	delivers, total, err := s.wmsDeliverRepository.List(ctx, params)
	if err != nil {
		return nil, err
	}

	// 将原始记录转换为API响应格式
	records := make([]*v1.WmsDeliverResponse, 0, len(delivers))
	for _, deliver := range delivers {
		response := &v1.WmsDeliverResponse{}
		if err := copier.Copy(response, deliver); err != nil {
			return nil, err
		}

		response.CreatedAt = deliver.CreatedAt.Format(time.RFC3339)
		response.UpdatedAt = deliver.UpdatedAt.Format(time.RFC3339)

		// 展开客户信息
		if slices.Contains(expand, "partner") && deliver.PartnerId > 0 {
			partner, err := s.wmsPartnerRepository.Get(ctx, deliver.PartnerId)
			if err == nil {
				partnerResponse := &v1.WmsPartnerResponse{}
				if err := copier.Copy(partnerResponse, partner); err == nil {
					partnerResponse.CreatedAt = partner.CreatedAt.Format(time.RFC3339)
					partnerResponse.UpdatedAt = partner.UpdatedAt.Format(time.RFC3339)
					response.Partner = partnerResponse
				}
			}
		}

		records = append(records, response)
	}

	return &pagination.Result{
		Records: records,
		Total:   total,
	}, nil
}
