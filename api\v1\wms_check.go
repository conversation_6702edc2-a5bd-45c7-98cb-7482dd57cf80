package v1

import "github.com/lib/pq"

type WmsCheckCreateParams struct {
	Code      string        `json:"code" binding:"required,max=64" example:"CHK202312010001"`
	Type      uint          `json:"type" binding:"required" example:"1"`
	AreaPath  pq.Int64Array `json:"areaPath" binding:"required" swaggertype:"array,integer" example:"1,2,3"`
	Summary   string        `json:"summary" example:"库存盘点"`
	Status    uint          `json:"status" example:"1"`
	TenantId  uint          `json:"tenantId" example:"1"`
	CreatedBy string        `json:"createdBy" example:"管理员"`
	UpdatedBy string        `json:"updatedBy" example:"管理员"`
}

type WmsCheckUpdateParams struct {
	WmsCheckCreateParams
}

type WmsCheckResponse struct {
	ID        uint          `json:"id"`
	Code      string        `json:"code"`
	Type      uint          `json:"type"`
	AreaPath  pq.Int64Array `json:"areaPath"`
	Summary   string        `json:"summary"`
	Status    uint          `json:"status"`
	TenantId  uint          `json:"tenantId"`
	CreatedBy string        `json:"createdBy"`
	UpdatedBy string        `json:"updatedBy"`
	CreatedAt string        `json:"createdAt"`
	UpdatedAt string        `json:"updatedAt"`
}
