package handler

import (
	v1 "daisy-server/api/v1"
	"daisy-server/internal/service"
	"daisy-server/pkg/pagination"
	"net/http"
	"strconv"

	"github.com/gin-gonic/gin"
)

type WmsMetaHandler struct {
	*Handler
	wmsMetaService service.WmsMetaService
}

func NewWmsMetaHandler(
	handler *Handler,
	wmsMetaService service.WmsMetaService,
) *WmsMetaHandler {
	return &WmsMetaHandler{
		Handler:        handler,
		wmsMetaService: wmsMetaService,
	}
}

// Create godoc
// @Summary 创建物料分类
// @Schemes
// @Description 创建新的物料分类记录
// @Tags 仓储模块,物料分类管理
// @Accept json
// @Produce json
// @Security Bearer
// @Param request body v1.WmsMetaCreateParams true "物料分类信息"
// @Success 200 {object} v1.Response
// @Router /wms/metas [post]
func (h *WmsMetaHandler) Create(ctx *gin.Context) {
	var req v1.WmsMetaCreateParams
	if err := ctx.ShouldBindJSON(&req); err != nil {
		v1.HandleError(ctx, http.StatusBadRequest, v1.ErrBadRequest, nil)
		return
	}

	if err := h.wmsMetaService.Create(ctx, &req); err != nil {
		v1.HandleError(ctx, http.StatusInternalServerError, err, nil)
		return
	}

	v1.HandleSuccess(ctx, nil)
}

// Update godoc
// @Summary 更新物料分类
// @Schemes
// @Description 更新指定ID的物料分类信息
// @Tags 仓储模块,物料分类管理
// @Accept json
// @Produce json
// @Security Bearer
// @Param id path int true "物料分类ID"
// @Param request body v1.WmsMetaUpdateParams true "物料分类信息"
// @Success 200 {object} v1.Response
// @Router /wms/metas/{id} [patch]
func (h *WmsMetaHandler) Update(ctx *gin.Context) {
	idStr := ctx.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		v1.HandleError(ctx, http.StatusBadRequest, v1.ErrBadRequest, nil)
		return
	}

	var req v1.WmsMetaUpdateParams
	if err := ctx.ShouldBindJSON(&req); err != nil {
		v1.HandleError(ctx, http.StatusBadRequest, v1.ErrBadRequest, nil)
		return
	}

	if err := h.wmsMetaService.Update(ctx, uint(id), &req); err != nil {
		v1.HandleError(ctx, http.StatusInternalServerError, err, nil)
		return
	}

	v1.HandleSuccess(ctx, nil)
}

// Delete godoc
// @Summary 删除物料分类
// @Schemes
// @Description 删除指定ID的物料分类
// @Tags 仓储模块,物料分类管理
// @Accept json
// @Produce json
// @Security Bearer
// @Param id path int true "物料分类ID"
// @Success 200 {object} v1.Response
// @Router /wms/metas/{id} [delete]
func (h *WmsMetaHandler) Delete(ctx *gin.Context) {
	idStr := ctx.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		v1.HandleError(ctx, http.StatusBadRequest, v1.ErrBadRequest, nil)
		return
	}

	if err := h.wmsMetaService.Delete(ctx, uint(id)); err != nil {
		v1.HandleError(ctx, http.StatusInternalServerError, err, nil)
		return
	}

	v1.HandleSuccess(ctx, nil)
}

// BatchDelete godoc
// @Summary 批量删除物料分类
// @Schemes
// @Description 批量删除指定IDs的物料分类
// @Tags 仓储模块,物料分类管理
// @Accept json
// @Produce json
// @Security Bearer
// @Param request body v1.BatchDeleteParams true "物料分类IDs"
// @Success 200 {object} v1.Response
// @Router /wms/metas [delete]
func (h *WmsMetaHandler) BatchDelete(ctx *gin.Context) {
	var req v1.BatchDeleteParams
	if err := ctx.ShouldBindJSON(&req); err != nil {
		v1.HandleError(ctx, http.StatusBadRequest, v1.ErrBadRequest, nil)
		return
	}

	if err := h.wmsMetaService.BatchDelete(ctx, req.IDs); err != nil {
		v1.HandleError(ctx, http.StatusInternalServerError, err, nil)
		return
	}

	v1.HandleSuccess(ctx, nil)
}

// Get godoc
// @Summary 获取物料分类
// @Schemes
// @Description 获取指定ID的物料分类信息
// @Tags 仓储模块,物料分类管理
// @Accept json
// @Produce json
// @Security Bearer
// @Param id path int true "物料分类ID"
// @Success 200 {object} v1.Response{data=v1.WmsMetaResponse}
// @Router /wms/metas/{id} [get]
func (h *WmsMetaHandler) Get(ctx *gin.Context) {
	idStr := ctx.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		v1.HandleError(ctx, http.StatusBadRequest, v1.ErrBadRequest, nil)
		return
	}

	meta, err := h.wmsMetaService.Get(ctx, uint(id))
	if err != nil {
		v1.HandleError(ctx, http.StatusInternalServerError, err, nil)
		return
	}

	v1.HandleSuccess(ctx, meta)
}

// List godoc
// @Summary 获取物料分类列表
// @Schemes
// @Description 分页获取物料分类列表
// @Tags 仓储模块,物料分类管理
// @Accept json
// @Produce json
// @Security Bearer
// @Param _page query int false "当前页码(默认值: 1)" example:"1"
// @Param _limit query int false "每页数量(默认值: 10)" example:"10"
// @Param _sort query string false "排序字段，多个字段用逗号分隔" example:"id"
// @Param _order query string false "排序方式，asc或desc，多个方式用逗号分隔" example:"desc"
// @Param q query string false "全文搜索" example:"关键词"
// @Param status query bool false "状态筛选" example:"true"
// @Param parentId query int false "父级ID筛选" example:"0"
// @Success 200 {object} v1.Response{data=pagination.Result{records=[]v1.WmsMetaResponse}}
// @Router /wms/metas [get]
func (h *WmsMetaHandler) List(ctx *gin.Context) {
	var params pagination.Params
	if err := ctx.ShouldBindQuery(&params); err != nil {
		v1.HandleError(ctx, http.StatusBadRequest, v1.ErrBadRequest, nil)
		return
	}

	// 状态筛选
	if status := ctx.DefaultQuery("status", ""); status != "" {
		params.AddFilter("status", status)
	}

	// 名称筛选
	if name := ctx.DefaultQuery("name", ""); name != "" {
		params.AddFilter("name_like", name)
	}

	// 父级ID筛选
	if parentId := ctx.DefaultQuery("parentId", ""); parentId != "" {
		params.AddFilter("parent_id", parentId)
	}

	result, err := h.wmsMetaService.List(ctx, &params)
	if err != nil {
		v1.HandleError(ctx, http.StatusInternalServerError, err, nil)
		return
	}

	v1.HandleSuccess(ctx, result)
}
