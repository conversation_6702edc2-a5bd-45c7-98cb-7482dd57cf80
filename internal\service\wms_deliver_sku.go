package service

import (
	"context"
	v1 "daisy-server/api/v1"
	"daisy-server/internal/model"
	"daisy-server/internal/repository"
	"daisy-server/pkg/pagination"
	"slices"
	"time"

	"github.com/jinzhu/copier"
)

type WmsDeliverSkuService interface {
	Create(ctx context.Context, req *v1.WmsDeliverSkuCreateParams) error
	Update(ctx context.Context, id uint, req *v1.WmsDeliverSkuUpdateParams) error
	Delete(ctx context.Context, id uint) error
	BatchDelete(ctx context.Context, ids []uint) error
	Get(ctx context.Context, id uint, expand []string) (*v1.WmsDeliverSkuResponse, error)
	List(ctx context.Context, params *pagination.Params, expand []string) (*pagination.Result, error)
}

func NewWmsDeliverSkuService(
	service *Service,
	wmsDeliverSkuRepository repository.WmsDeliverSkuRepository,
	wmsDeliverRepository repository.WmsDeliverRepository,
	wmsItemRepository repository.WmsItemRepository,
	wmsSkuRepository repository.WmsSkuRepository,
) WmsDeliverSkuService {
	return &wmsDeliverSkuService{
		Service:                 service,
		wmsDeliverSkuRepository: wmsDeliverSkuRepository,
		wmsDeliverRepository:    wmsDeliverRepository,
		wmsItemRepository:       wmsItemRepository,
		wmsSkuRepository:        wmsSkuRepository,
	}
}

type wmsDeliverSkuService struct {
	*Service
	wmsDeliverSkuRepository repository.WmsDeliverSkuRepository
	wmsDeliverRepository    repository.WmsDeliverRepository
	wmsItemRepository       repository.WmsItemRepository
	wmsSkuRepository        repository.WmsSkuRepository
}

// 出库单物料明细相关方法实现
func (s *wmsDeliverSkuService) Create(ctx context.Context, req *v1.WmsDeliverSkuCreateParams) error {
	deliverSku := &model.WmsDeliverSku{}
	if err := copier.Copy(deliverSku, req); err != nil {
		return err
	}

	// 获取当前用户信息
	user := ctx.Value("user").(*model.SysUser)

	// 租户ID
	deliverSku.TenantId = user.TenantId

	// 创建人
	if user.Nickname != "" {
		deliverSku.CreatedBy = user.Nickname
	} else {
		deliverSku.CreatedBy = user.Username
	}

	return s.tm.Transaction(ctx, func(ctx context.Context) error {
		if err := s.wmsDeliverSkuRepository.Create(ctx, deliverSku); err != nil {
			return err
		}
		return nil
	})
}

func (s *wmsDeliverSkuService) Update(ctx context.Context, id uint, req *v1.WmsDeliverSkuUpdateParams) error {
	deliverSku, err := s.wmsDeliverSkuRepository.Get(ctx, id)
	if err != nil {
		return err
	}

	// 获取当前用户信息
	user := ctx.Value("user").(*model.SysUser)

	// 过滤非本租户数据
	if deliverSku.TenantId != user.TenantId {
		return v1.ErrForbidden
	}

	if err := copier.Copy(deliverSku, req); err != nil {
		return err
	}

	// 更新人
	if user.Nickname != "" {
		deliverSku.UpdatedBy = user.Nickname
	} else {
		deliverSku.UpdatedBy = user.Username
	}

	return s.tm.Transaction(ctx, func(ctx context.Context) error {
		if err := s.wmsDeliverSkuRepository.Update(ctx, deliverSku); err != nil {
			return err
		}
		return nil
	})
}

func (s *wmsDeliverSkuService) Delete(ctx context.Context, id uint) error {
	// 获取当前用户信息
	user := ctx.Value("user").(*model.SysUser)

	// 是否是该租户数据
	deliverSku, err := s.wmsDeliverSkuRepository.Get(ctx, id)
	if err != nil {
		return err
	}
	if deliverSku.TenantId != user.TenantId {
		return v1.ErrForbidden
	}

	return s.tm.Transaction(ctx, func(ctx context.Context) error {
		if err := s.wmsDeliverSkuRepository.Delete(ctx, id); err != nil {
			return err
		}
		return nil
	})
}

func (s *wmsDeliverSkuService) BatchDelete(ctx context.Context, ids []uint) error {
	// 获取当前用户信息
	user := ctx.Value("user").(*model.SysUser)

	// 获取目标记录
	deliverSkus, err := s.wmsDeliverSkuRepository.GetByIds(ctx, ids)
	if err != nil {
		return err
	}

	// 过滤非本租户数据
	var newIds []uint
	for _, deliverSku := range deliverSkus {
		if deliverSku.TenantId == user.TenantId {
			newIds = append(newIds, deliverSku.ID)
		}
	}

	return s.tm.Transaction(ctx, func(ctx context.Context) error {
		if err := s.wmsDeliverSkuRepository.BatchDelete(ctx, newIds); err != nil {
			return err
		}
		return nil
	})
}

func (s *wmsDeliverSkuService) Get(ctx context.Context, id uint, expand []string) (*v1.WmsDeliverSkuResponse, error) {
	deliverSku, err := s.wmsDeliverSkuRepository.Get(ctx, id)
	if err != nil {
		return nil, err
	}

	// 获取当前用户信息
	user := ctx.Value("user").(*model.SysUser)

	// 过滤非本租户数据
	if deliverSku.TenantId != user.TenantId {
		return nil, v1.ErrForbidden
	}

	response := &v1.WmsDeliverSkuResponse{}
	if err := copier.Copy(response, deliverSku); err != nil {
		return nil, err
	}

	response.CreatedAt = deliverSku.CreatedAt.Format(time.RFC3339)
	response.UpdatedAt = deliverSku.UpdatedAt.Format(time.RFC3339)

	// 展开出库单信息
	if slices.Contains(expand, "deliver") && deliverSku.DeliverId > 0 {
		deliver, err := s.wmsDeliverRepository.Get(ctx, deliverSku.DeliverId)
		if err == nil {
			deliverResponse := &v1.WmsDeliverResponse{}
			if err := copier.Copy(deliverResponse, deliver); err == nil {
				deliverResponse.CreatedAt = deliver.CreatedAt.Format(time.RFC3339)
				deliverResponse.UpdatedAt = deliver.UpdatedAt.Format(time.RFC3339)
				response.Deliver = deliverResponse
			}
		}
	}

	// 展开物料信息
	if slices.Contains(expand, "item") && deliverSku.ItemId > 0 {
		item, err := s.wmsItemRepository.Get(ctx, deliverSku.ItemId)
		if err == nil {
			itemResponse := &v1.WmsItemResponse{}
			if err := copier.Copy(itemResponse, item); err == nil {
				itemResponse.CreatedAt = item.CreatedAt.Format(time.RFC3339)
				itemResponse.UpdatedAt = item.UpdatedAt.Format(time.RFC3339)
				response.Item = itemResponse
			}
		}
	}

	// 展开规格信息
	if slices.Contains(expand, "sku") && deliverSku.SkuId > 0 {
		sku, err := s.wmsSkuRepository.Get(ctx, deliverSku.SkuId)
		if err == nil {
			skuResponse := &v1.WmsSkuResponse{}
			if err := copier.Copy(skuResponse, sku); err == nil {
				skuResponse.CreatedAt = sku.CreatedAt.Format(time.RFC3339)
				skuResponse.UpdatedAt = sku.UpdatedAt.Format(time.RFC3339)
				response.Sku = skuResponse
			}
		}
	}

	return response, nil
}

func (s *wmsDeliverSkuService) List(ctx context.Context, params *pagination.Params, expand []string) (*pagination.Result, error) {
	// 获取当前用户信息
	user := ctx.Value("user").(*model.SysUser)

	// 过滤非本租户数据
	params.AddFilter("tenant_id", user.TenantId)

	deliverSkus, total, err := s.wmsDeliverSkuRepository.List(ctx, params)
	if err != nil {
		return nil, err
	}

	// 将原始记录转换为API响应格式
	records := make([]*v1.WmsDeliverSkuResponse, 0, len(deliverSkus))
	for _, deliverSku := range deliverSkus {
		response := &v1.WmsDeliverSkuResponse{}
		if err := copier.Copy(response, deliverSku); err != nil {
			return nil, err
		}

		response.CreatedAt = deliverSku.CreatedAt.Format(time.RFC3339)
		response.UpdatedAt = deliverSku.UpdatedAt.Format(time.RFC3339)

		// 展开出库单信息
		if slices.Contains(expand, "deliver") && deliverSku.DeliverId > 0 {
			deliver, err := s.wmsDeliverRepository.Get(ctx, deliverSku.DeliverId)
			if err == nil {
				deliverResponse := &v1.WmsDeliverResponse{}
				if err := copier.Copy(deliverResponse, deliver); err == nil {
					deliverResponse.CreatedAt = deliver.CreatedAt.Format(time.RFC3339)
					deliverResponse.UpdatedAt = deliver.UpdatedAt.Format(time.RFC3339)
					response.Deliver = deliverResponse
				}
			}
		}

		// 展开物料信息
		if slices.Contains(expand, "item") && deliverSku.ItemId > 0 {
			item, err := s.wmsItemRepository.Get(ctx, deliverSku.ItemId)
			if err == nil {
				itemResponse := &v1.WmsItemResponse{}
				if err := copier.Copy(itemResponse, item); err == nil {
					itemResponse.CreatedAt = item.CreatedAt.Format(time.RFC3339)
					itemResponse.UpdatedAt = item.UpdatedAt.Format(time.RFC3339)
					response.Item = itemResponse
				}
			}
		}

		// 展开规格信息
		if slices.Contains(expand, "sku") && deliverSku.SkuId > 0 {
			sku, err := s.wmsSkuRepository.Get(ctx, deliverSku.SkuId)
			if err == nil {
				skuResponse := &v1.WmsSkuResponse{}
				if err := copier.Copy(skuResponse, sku); err == nil {
					skuResponse.CreatedAt = sku.CreatedAt.Format(time.RFC3339)
					skuResponse.UpdatedAt = sku.UpdatedAt.Format(time.RFC3339)
					response.Sku = skuResponse
				}
			}
		}

		records = append(records, response)
	}

	return &pagination.Result{
		Records: records,
		Total:   total,
	}, nil
}
