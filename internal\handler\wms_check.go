package handler

import (
	v1 "daisy-server/api/v1"
	"daisy-server/internal/service"
	"daisy-server/pkg/pagination"
	"net/http"
	"strconv"

	"github.com/gin-gonic/gin"
)

type WmsCheckHandler struct {
	*Handler
	wmsCheckService service.WmsCheckService
}

func NewWmsCheckHandler(
	handler *Handler,
	wmsCheckService service.WmsCheckService,
) *WmsCheckHandler {
	return &WmsCheckHandler{
		Handler:         handler,
		wmsCheckService: wmsCheckService,
	}
}

// Create godoc
// @Summary 创建盘点单
// @Schemes
// @Description 创建新的盘点单记录
// @Tags 仓储模块,盘点单管理
// @Accept json
// @Produce json
// @Security Bearer
// @Param request body v1.WmsCheckCreateParams true "盘点单信息"
// @Success 200 {object} v1.Response
// @Router /wms/checks [post]
func (h *WmsCheckHandler) Create(ctx *gin.Context) {
	var req v1.WmsCheckCreateParams
	if err := ctx.ShouldBindJSON(&req); err != nil {
		v1.HandleError(ctx, http.StatusBadRequest, v1.ErrBadRequest, nil)
		return
	}

	if err := h.wmsCheckService.Create(ctx, &req); err != nil {
		v1.HandleError(ctx, http.StatusInternalServerError, err, nil)
		return
	}

	v1.HandleSuccess(ctx, nil)
}

// Update godoc
// @Summary 更新盘点单
// @Schemes
// @Description 更新指定ID的盘点单信息
// @Tags 仓储模块,盘点单管理
// @Accept json
// @Produce json
// @Security Bearer
// @Param id path int true "盘点单ID"
// @Param request body v1.WmsCheckUpdateParams true "盘点单信息"
// @Success 200 {object} v1.Response
// @Router /wms/checks/{id} [patch]
func (h *WmsCheckHandler) Update(ctx *gin.Context) {
	idStr := ctx.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		v1.HandleError(ctx, http.StatusBadRequest, v1.ErrBadRequest, nil)
		return
	}

	var req v1.WmsCheckUpdateParams
	if err := ctx.ShouldBindJSON(&req); err != nil {
		v1.HandleError(ctx, http.StatusBadRequest, v1.ErrBadRequest, nil)
		return
	}

	if err := h.wmsCheckService.Update(ctx, uint(id), &req); err != nil {
		v1.HandleError(ctx, http.StatusInternalServerError, err, nil)
		return
	}

	v1.HandleSuccess(ctx, nil)
}

// Delete godoc
// @Summary 删除盘点单
// @Schemes
// @Description 删除指定ID的盘点单
// @Tags 仓储模块,盘点单管理
// @Accept json
// @Produce json
// @Security Bearer
// @Param id path int true "盘点单ID"
// @Success 200 {object} v1.Response
// @Router /wms/checks/{id} [delete]
func (h *WmsCheckHandler) Delete(ctx *gin.Context) {
	idStr := ctx.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		v1.HandleError(ctx, http.StatusBadRequest, v1.ErrBadRequest, nil)
		return
	}

	if err := h.wmsCheckService.Delete(ctx, uint(id)); err != nil {
		v1.HandleError(ctx, http.StatusInternalServerError, err, nil)
		return
	}

	v1.HandleSuccess(ctx, nil)
}

// BatchDelete godoc
// @Summary 批量删除盘点单
// @Schemes
// @Description 批量删除指定IDs的盘点单
// @Tags 仓储模块,盘点单管理
// @Accept json
// @Produce json
// @Security Bearer
// @Param request body v1.BatchDeleteParams true "盘点单IDs"
// @Success 200 {object} v1.Response
// @Router /wms/checks [delete]
func (h *WmsCheckHandler) BatchDelete(ctx *gin.Context) {
	var req v1.BatchDeleteParams
	if err := ctx.ShouldBindJSON(&req); err != nil {
		v1.HandleError(ctx, http.StatusBadRequest, v1.ErrBadRequest, nil)
		return
	}

	if err := h.wmsCheckService.BatchDelete(ctx, req.IDs); err != nil {
		v1.HandleError(ctx, http.StatusInternalServerError, err, nil)
		return
	}

	v1.HandleSuccess(ctx, nil)
}

// Get godoc
// @Summary 获取盘点单
// @Schemes
// @Description 获取指定ID的盘点单信息
// @Tags 仓储模块,盘点单管理
// @Accept json
// @Produce json
// @Security Bearer
// @Param id path int true "盘点单ID"
// @Success 200 {object} v1.Response{data=v1.WmsCheckResponse}
// @Router /wms/checks/{id} [get]
func (h *WmsCheckHandler) Get(ctx *gin.Context) {
	idStr := ctx.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		v1.HandleError(ctx, http.StatusBadRequest, v1.ErrBadRequest, nil)
		return
	}

	check, err := h.wmsCheckService.Get(ctx, uint(id))
	if err != nil {
		v1.HandleError(ctx, http.StatusInternalServerError, err, nil)
		return
	}

	v1.HandleSuccess(ctx, check)
}

// List godoc
// @Summary 获取盘点单列表
// @Schemes
// @Description 分页获取盘点单列表
// @Tags 仓储模块,盘点单管理
// @Accept json
// @Produce json
// @Security Bearer
// @Param _page query int false "当前页码(默认值: 1)" example:"1"
// @Param _limit query int false "每页数量(默认值: 10)" example:"10"
// @Param _sort query string false "排序字段，多个字段用逗号分隔" example:"id"
// @Param _order query string false "排序方式，asc或desc，多个方式用逗号分隔" example:"desc"
// @Param q query string false "全文搜索" example:"关键词"
// @Param status query int false "状态筛选 1草稿 2待审核 3作业中 4已完成 5已作废" example:"1"
// @Param code query string false "编号筛选" example:"CHK202312010001"
// @Param type query int false "类型筛选" example:"1"
// @Param areaId query string false "库位ID筛选" example:"1"
// @Success 200 {object} v1.Response{data=pagination.Result{records=[]v1.WmsCheckResponse}}
// @Router /wms/checks [get]
func (h *WmsCheckHandler) List(ctx *gin.Context) {
	var params pagination.Params
	if err := ctx.ShouldBindQuery(&params); err != nil {
		v1.HandleError(ctx, http.StatusBadRequest, v1.ErrBadRequest, nil)
		return
	}

	// 状态筛选
	if status := ctx.DefaultQuery("status", ""); status != "" {
		params.AddFilter("status", status)
	}

	// 编号筛选
	if code := ctx.DefaultQuery("code", ""); code != "" {
		params.AddFilter("code_like", code)
	}

	// 类型筛选
	if checkType := ctx.DefaultQuery("type", ""); checkType != "" {
		params.AddFilter("type", checkType)
	}

	// 库位筛选
	if areaId := ctx.DefaultQuery("areaId", ""); areaId != "" {
		params.AddFilter("area_path_any", areaId)
	}

	result, err := h.wmsCheckService.List(ctx, &params)
	if err != nil {
		v1.HandleError(ctx, http.StatusInternalServerError, err, nil)
		return
	}

	v1.HandleSuccess(ctx, result)
}
