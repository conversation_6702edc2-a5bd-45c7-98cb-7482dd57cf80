package repository

import (
	"context"
	v1 "daisy-server/api/v1"
	"daisy-server/internal/model"
	"daisy-server/pkg/pagination"
	"errors"

	"gorm.io/gorm"
)

type WmsDeliverSkuRepository interface {
	Create(ctx context.Context, deliverSku *model.WmsDeliverSku) error
	Update(ctx context.Context, deliverSku *model.WmsDeliverSku) error
	Delete(ctx context.Context, id uint) error
	BatchDelete(ctx context.Context, ids []uint) error
	Get(ctx context.Context, id uint) (*model.WmsDeliverSku, error)
	GetByIds(ctx context.Context, ids []uint) ([]*model.WmsDeliverSku, error)
	List(ctx context.Context, params *pagination.Params) ([]*model.WmsDeliverSku, int64, error)
}

func NewWmsDeliverSkuRepository(
	repository *Repository,
) WmsDeliverSkuRepository {
	return &wmsDeliverSkuRepository{
		Repository: repository,
	}
}

type wmsDeliverSkuRepository struct {
	*Repository
}

func (r *wmsDeliverSkuRepository) Create(ctx context.Context, deliverSku *model.WmsDeliverSku) error {
	if err := r.DB(ctx).Create(deliverSku).Error; err != nil {
		return err
	}
	return nil
}

func (r *wmsDeliverSkuRepository) Update(ctx context.Context, deliverSku *model.WmsDeliverSku) error {
	if err := r.DB(ctx).Save(deliverSku).Error; err != nil {
		return err
	}
	return nil
}

func (r *wmsDeliverSkuRepository) Delete(ctx context.Context, id uint) error {
	if err := r.DB(ctx).Delete(&model.WmsDeliverSku{}, id).Error; err != nil {
		return err
	}
	return nil
}

func (r *wmsDeliverSkuRepository) BatchDelete(ctx context.Context, ids []uint) error {
	if err := r.DB(ctx).Delete(&model.WmsDeliverSku{}, ids).Error; err != nil {
		return err
	}
	return nil
}

func (r *wmsDeliverSkuRepository) Get(ctx context.Context, id uint) (*model.WmsDeliverSku, error) {
	var deliverSku model.WmsDeliverSku
	if err := r.DB(ctx).First(&deliverSku, id).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, v1.ErrNotFound
		}
		return nil, err
	}
	return &deliverSku, nil
}

func (r *wmsDeliverSkuRepository) GetByIds(ctx context.Context, ids []uint) ([]*model.WmsDeliverSku, error) {
	var deliverSkus []*model.WmsDeliverSku
	if len(ids) == 0 {
		return deliverSkus, nil
	}
	
	if err := r.DB(ctx).Where("id IN ?", ids).Find(&deliverSkus).Error; err != nil {
		return nil, err
	}
	return deliverSkus, nil
}

func (r *wmsDeliverSkuRepository) List(ctx context.Context, params *pagination.Params) ([]*model.WmsDeliverSku, int64, error) {
	var records []*model.WmsDeliverSku
	var total int64

	db := r.DB(ctx).Model(&model.WmsDeliverSku{})

	// 使用GetResult方法直接获取分页结果
	total, err := params.GetResult(db, &records, &model.WmsDeliverSku{})
	if err != nil {
		return nil, 0, err
	}

	return records, total, nil
}
