package handler

import (
	v1 "daisy-server/api/v1"
	"daisy-server/internal/service"
	"daisy-server/pkg/pagination"
	"net/http"
	"strconv"

	"github.com/gin-gonic/gin"
)

type WmsSkuHandler struct {
	*Handler
	wmsSkuService service.WmsSkuService
}

func NewWmsSkuHandler(
	handler *Handler,
	wmsSkuService service.WmsSkuService,
) *WmsSkuHandler {
	return &WmsSkuHandler{
		Handler:       handler,
		wmsSkuService: wmsSkuService,
	}
}

// Create godoc
// @Summary 创建物料规格
// @Schemes
// @Description 创建新的物料规格记录
// @Tags 仓储模块,物料规格管理
// @Accept json
// @Produce json
// @Security Bearer
// @Param request body v1.WmsSkuCreateParams true "物料规格信息"
// @Success 200 {object} v1.Response
// @Router /wms/skus [post]
func (h *WmsSkuHandler) Create(ctx *gin.Context) {
	var req v1.WmsSkuCreateParams
	if err := ctx.ShouldBindJSON(&req); err != nil {
		v1.HandleError(ctx, http.StatusBadRequest, v1.ErrBadRequest, nil)
		return
	}

	if err := h.wmsSkuService.Create(ctx, &req); err != nil {
		v1.HandleError(ctx, http.StatusInternalServerError, err, nil)
		return
	}

	v1.HandleSuccess(ctx, nil)
}

// Update godoc
// @Summary 更新物料规格
// @Schemes
// @Description 更新指定ID的物料规格信息
// @Tags 仓储模块,物料规格管理
// @Accept json
// @Produce json
// @Security Bearer
// @Param id path int true "物料规格ID"
// @Param request body v1.WmsSkuUpdateParams true "物料规格信息"
// @Success 200 {object} v1.Response
// @Router /wms/skus/{id} [patch]
func (h *WmsSkuHandler) Update(ctx *gin.Context) {
	idStr := ctx.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		v1.HandleError(ctx, http.StatusBadRequest, v1.ErrBadRequest, nil)
		return
	}

	var req v1.WmsSkuUpdateParams
	if err := ctx.ShouldBindJSON(&req); err != nil {
		v1.HandleError(ctx, http.StatusBadRequest, v1.ErrBadRequest, nil)
		return
	}

	if err := h.wmsSkuService.Update(ctx, uint(id), &req); err != nil {
		v1.HandleError(ctx, http.StatusInternalServerError, err, nil)
		return
	}

	v1.HandleSuccess(ctx, nil)
}

// Delete godoc
// @Summary 删除物料规格
// @Schemes
// @Description 删除指定ID的物料规格
// @Tags 仓储模块,物料规格管理
// @Accept json
// @Produce json
// @Security Bearer
// @Param id path int true "物料规格ID"
// @Success 200 {object} v1.Response
// @Router /wms/skus/{id} [delete]
func (h *WmsSkuHandler) Delete(ctx *gin.Context) {
	idStr := ctx.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		v1.HandleError(ctx, http.StatusBadRequest, v1.ErrBadRequest, nil)
		return
	}

	if err := h.wmsSkuService.Delete(ctx, uint(id)); err != nil {
		v1.HandleError(ctx, http.StatusInternalServerError, err, nil)
		return
	}

	v1.HandleSuccess(ctx, nil)
}

// BatchDelete godoc
// @Summary 批量删除物料规格
// @Schemes
// @Description 批量删除指定IDs的物料规格
// @Tags 仓储模块,物料规格管理
// @Accept json
// @Produce json
// @Security Bearer
// @Param request body v1.BatchDeleteParams true "物料规格IDs"
// @Success 200 {object} v1.Response
// @Router /wms/skus [delete]
func (h *WmsSkuHandler) BatchDelete(ctx *gin.Context) {
	var req v1.BatchDeleteParams
	if err := ctx.ShouldBindJSON(&req); err != nil {
		v1.HandleError(ctx, http.StatusBadRequest, v1.ErrBadRequest, nil)
		return
	}

	if err := h.wmsSkuService.BatchDelete(ctx, req.IDs); err != nil {
		v1.HandleError(ctx, http.StatusInternalServerError, err, nil)
		return
	}

	v1.HandleSuccess(ctx, nil)
}

// Get godoc
// @Summary 获取物料规格
// @Schemes
// @Description 获取指定ID的物料规格信息
// @Tags 仓储模块,物料规格管理
// @Accept json
// @Produce json
// @Security Bearer
// @Param id path int true "物料规格ID"
// @Success 200 {object} v1.Response{data=v1.WmsSkuResponse}
// @Router /wms/skus/{id} [get]
func (h *WmsSkuHandler) Get(ctx *gin.Context) {
	idStr := ctx.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		v1.HandleError(ctx, http.StatusBadRequest, v1.ErrBadRequest, nil)
		return
	}

	sku, err := h.wmsSkuService.Get(ctx, uint(id))
	if err != nil {
		v1.HandleError(ctx, http.StatusInternalServerError, err, nil)
		return
	}

	v1.HandleSuccess(ctx, sku)
}

// List godoc
// @Summary 获取物料规格列表
// @Schemes
// @Description 分页获取物料规格列表
// @Tags 仓储模块,物料规格管理
// @Accept json
// @Produce json
// @Security Bearer
// @Param _page query int false "当前页码(默认值: 1)" example:"1"
// @Param _limit query int false "每页数量(默认值: 10)" example:"10"
// @Param _sort query string false "排序字段，多个字段用逗号分隔" example:"id"
// @Param _order query string false "排序方式，asc或desc，多个方式用逗号分隔" example:"desc"
// @Param q query string false "全文搜索" example:"关键词"
// @Param status query bool false "状态筛选" example:"true"
// @Param itemId query int false "物料ID筛选" example:"1"
// @Param unit query string false "单位筛选" example:"个"
// @Param min query number false "最小库存筛选" example:"10.5"
// @Success 200 {object} v1.Response{data=pagination.Result{records=[]v1.WmsSkuResponse}}
// @Router /wms/skus [get]
func (h *WmsSkuHandler) List(ctx *gin.Context) {
	var params pagination.Params
	if err := ctx.ShouldBindQuery(&params); err != nil {
		v1.HandleError(ctx, http.StatusBadRequest, v1.ErrBadRequest, nil)
		return
	}

	// 状态筛选
	if status := ctx.DefaultQuery("status", ""); status != "" {
		params.AddFilter("status", status)
	}

	// 名称筛选
	if name := ctx.DefaultQuery("name", ""); name != "" {
		params.AddFilter("name_like", name)
	}

	// 编号筛选
	if code := ctx.DefaultQuery("code", ""); code != "" {
		params.AddFilter("code_like", code)
	}

	// 物料ID筛选
	if itemId := ctx.DefaultQuery("itemId", ""); itemId != "" {
		params.AddFilter("item_id", itemId)
	}

	// 单位筛选
	if unit := ctx.DefaultQuery("unit", ""); unit != "" {
		params.AddFilter("unit", unit)
	}

	// 最小库存筛选
	if min := ctx.DefaultQuery("min", ""); min != "" {
		params.AddFilter("min_gte", min)
	}

	result, err := h.wmsSkuService.List(ctx, &params)
	if err != nil {
		v1.HandleError(ctx, http.StatusInternalServerError, err, nil)
		return
	}

	v1.HandleSuccess(ctx, result)
}
