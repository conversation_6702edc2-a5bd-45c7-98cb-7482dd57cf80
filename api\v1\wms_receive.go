package v1

type WmsReceiveCreateParams struct {
	Code      string `json:"code" binding:"required,max=64" example:"REC202312010001"`
	Type      uint   `json:"type" binding:"required" example:"1"`
	PartnerId uint   `json:"partnerId" example:"1"`
	RelatedNo string `json:"relatedNo" binding:"max=64" example:"PO202312010001"`
	Summary   string `json:"summary" example:"采购入库"`
	Status    uint   `json:"status" example:"1"`
	TenantId  uint   `json:"tenantId" example:"1"`
	CreatedBy string `json:"createdBy" example:"管理员"`
	UpdatedBy string `json:"updatedBy" example:"管理员"`
}

type WmsReceiveUpdateParams struct {
	WmsReceiveCreateParams
}

type WmsReceiveResponse struct {
	ID        uint                `json:"id"`
	Code      string              `json:"code"`
	Type      uint                `json:"type"`
	PartnerId uint                `json:"partnerId"`
	RelatedNo string              `json:"relatedNo"`
	Summary   string              `json:"summary"`
	Status    uint                `json:"status"`
	TenantId  uint                `json:"tenantId"`
	CreatedBy string              `json:"createdBy"`
	UpdatedBy string              `json:"updatedBy"`
	CreatedAt string              `json:"createdAt"`
	UpdatedAt string              `json:"updatedAt"`
	Partner   *WmsPartnerResponse `json:"partner,omitempty"` // 供应商信息
}
