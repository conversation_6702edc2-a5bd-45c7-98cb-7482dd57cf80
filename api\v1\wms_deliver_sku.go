package v1

import "gorm.io/datatypes"

type WmsDeliverSkuCreateParams struct {
	DeliverId uint           `json:"deliverId" binding:"required" example:"1"`
	ItemId    uint           `json:"itemId" binding:"required" example:"1"`
	SkuId     uint           `json:"skuId" binding:"required" example:"1"`
	Num       float64        `json:"num" binding:"min=0" example:"100.50"`
	AreaNum   datatypes.JSON `json:"areaNum" swaggertype:"object" example:"[{\"areaPath\":[1,2,3],\"num\":50}]"`
	Pcs       float64        `json:"pcs" binding:"min=0" example:"10.00"`
	Status    bool           `json:"status" example:"false"`
	TenantId  uint           `json:"tenantId" example:"1"`
	CreatedBy string         `json:"createdBy" example:"管理员"`
	UpdatedBy string         `json:"updatedBy" example:"管理员"`
}

type WmsDeliverSkuUpdateParams struct {
	WmsDeliverSkuCreateParams
}

type WmsDeliverSkuResponse struct {
	ID        uint                `json:"id"`
	DeliverId uint                `json:"deliverId"`
	ItemId    uint                `json:"itemId"`
	SkuId     uint                `json:"skuId"`
	Num       float64             `json:"num"`
	AreaNum   datatypes.JSON      `json:"areaNum"`
	Pcs       float64             `json:"pcs"`
	Status    bool                `json:"status"`
	TenantId  uint                `json:"tenantId"`
	CreatedBy string              `json:"createdBy"`
	UpdatedBy string              `json:"updatedBy"`
	CreatedAt string              `json:"createdAt"`
	UpdatedAt string              `json:"updatedAt"`
	Deliver   *WmsDeliverResponse `json:"deliver,omitempty"` // 出库单信息
	Item      *WmsItemResponse    `json:"item,omitempty"`    // 物料信息
	Sku       *WmsSkuResponse     `json:"sku,omitempty"`     // 规格信息
}
