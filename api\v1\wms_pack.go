package v1

type WmsPackCreateParams struct {
	Code      string  `json:"code" binding:"required,max=64" example:"PACK202312010001"`
	DeliverId uint    `json:"deliverId" binding:"required" example:"1"`
	ItemId    uint    `json:"itemId" binding:"required" example:"1"`
	SkuId     uint    `json:"skuId" binding:"required" example:"1"`
	Num       float64 `json:"num" binding:"required,min=0" example:"100.50"`
	TenantId  uint    `json:"tenantId" example:"1"`
	CreatedBy string  `json:"createdBy" example:"管理员"`
	UpdatedBy string  `json:"updatedBy" example:"管理员"`
}

type WmsPackUpdateParams struct {
	WmsPackCreateParams
}

type WmsPackResponse struct {
	ID        uint                 `json:"id"`
	Code      string               `json:"code"`
	DeliverId uint                 `json:"deliverId"`
	ItemId    uint                 `json:"itemId"`
	SkuId     uint                 `json:"skuId"`
	Num       float64              `json:"num"`
	TenantId  uint                 `json:"tenantId"`
	CreatedBy string               `json:"createdBy"`
	UpdatedBy string               `json:"updatedBy"`
	CreatedAt string               `json:"createdAt"`
	UpdatedAt string               `json:"updatedAt"`
	Deliver   *WmsDeliverResponse  `json:"deliver,omitempty"` // 出库单信息
	Item      *WmsItemResponse     `json:"item,omitempty"`    // 物料信息
	Sku       *WmsSkuResponse      `json:"sku,omitempty"`     // 规格信息
}
