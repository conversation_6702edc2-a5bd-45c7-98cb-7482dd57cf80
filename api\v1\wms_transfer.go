package v1

type WmsTransferCreateParams struct {
	Code      string `json:"code" binding:"required,max=64" example:"TRF202312010001"`
	Type      uint   `json:"type" binding:"required" example:"1"`
	Summary   string `json:"summary" example:"库位调拨"`
	Status    uint   `json:"status" example:"1"`
	TenantId  uint   `json:"tenantId" example:"1"`
	CreatedBy string `json:"createdBy" example:"管理员"`
	UpdatedBy string `json:"updatedBy" example:"管理员"`
}

type WmsTransferUpdateParams struct {
	WmsTransferCreateParams
}

type WmsTransferResponse struct {
	ID        uint   `json:"id"`
	Code      string `json:"code"`
	Type      uint   `json:"type"`
	Summary   string `json:"summary"`
	Status    uint   `json:"status"`
	TenantId  uint   `json:"tenantId"`
	CreatedBy string `json:"createdBy"`
	UpdatedBy string `json:"updatedBy"`
	CreatedAt string `json:"createdAt"`
	UpdatedAt string `json:"updatedAt"`
}
