package repository

import (
	"context"
	v1 "daisy-server/api/v1"
	"daisy-server/internal/model"
	"daisy-server/pkg/pagination"
	"errors"

	"gorm.io/gorm"
)

type WmsCheckSkuRepository interface {
	Create(ctx context.Context, checkSku *model.WmsCheckSku) error
	Update(ctx context.Context, checkSku *model.WmsCheckSku) error
	Delete(ctx context.Context, id uint) error
	BatchDelete(ctx context.Context, ids []uint) error
	Get(ctx context.Context, id uint) (*model.WmsCheckSku, error)
	GetByIds(ctx context.Context, ids []uint) ([]*model.WmsCheckSku, error)
	List(ctx context.Context, params *pagination.Params) ([]*model.WmsCheckSku, int64, error)
}

func NewWmsCheckSkuRepository(
	repository *Repository,
) WmsCheckSkuRepository {
	return &wmsCheckSkuRepository{
		Repository: repository,
	}
}

type wmsCheckSkuRepository struct {
	*Repository
}

func (r *wmsCheckSkuRepository) Create(ctx context.Context, checkSku *model.WmsCheckSku) error {
	if err := r.DB(ctx).Create(checkSku).Error; err != nil {
		return err
	}
	return nil
}

func (r *wmsCheckSkuRepository) Update(ctx context.Context, checkSku *model.WmsCheckSku) error {
	if err := r.DB(ctx).Save(checkSku).Error; err != nil {
		return err
	}
	return nil
}

func (r *wmsCheckSkuRepository) Delete(ctx context.Context, id uint) error {
	if err := r.DB(ctx).Delete(&model.WmsCheckSku{}, id).Error; err != nil {
		return err
	}
	return nil
}

func (r *wmsCheckSkuRepository) BatchDelete(ctx context.Context, ids []uint) error {
	if err := r.DB(ctx).Delete(&model.WmsCheckSku{}, ids).Error; err != nil {
		return err
	}
	return nil
}

func (r *wmsCheckSkuRepository) Get(ctx context.Context, id uint) (*model.WmsCheckSku, error) {
	var checkSku model.WmsCheckSku
	if err := r.DB(ctx).First(&checkSku, id).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, v1.ErrNotFound
		}
		return nil, err
	}
	return &checkSku, nil
}

func (r *wmsCheckSkuRepository) GetByIds(ctx context.Context, ids []uint) ([]*model.WmsCheckSku, error) {
	var checkSkus []*model.WmsCheckSku
	if len(ids) == 0 {
		return checkSkus, nil
	}
	
	if err := r.DB(ctx).Where("id IN ?", ids).Find(&checkSkus).Error; err != nil {
		return nil, err
	}
	return checkSkus, nil
}

func (r *wmsCheckSkuRepository) List(ctx context.Context, params *pagination.Params) ([]*model.WmsCheckSku, int64, error) {
	var records []*model.WmsCheckSku
	var total int64

	db := r.DB(ctx).Model(&model.WmsCheckSku{})

	// 使用GetResult方法直接获取分页结果
	total, err := params.GetResult(db, &records, &model.WmsCheckSku{})
	if err != nil {
		return nil, 0, err
	}

	return records, total, nil
}
