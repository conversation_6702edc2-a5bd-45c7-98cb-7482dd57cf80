package handler

import (
	v1 "daisy-server/api/v1"
	"daisy-server/internal/service"
	"daisy-server/pkg/pagination"
	"net/http"
	"strconv"
	"strings"

	"github.com/gin-gonic/gin"
)

type WmsDeliverSkuHandler struct {
	*Handler
	wmsDeliverSkuService service.WmsDeliverSkuService
}

func NewWmsDeliverSkuHandler(
	handler *Handler,
	wmsDeliverSkuService service.WmsDeliverSkuService,
) *WmsDeliverSkuHandler {
	return &WmsDeliverSkuHandler{
		Handler:              handler,
		wmsDeliverSkuService: wmsDeliverSkuService,
	}
}

// Create godoc
// @Summary 创建出库单物料规格
// @Schemes
// @Description 创建新的出库单物料规格记录
// @Tags 仓储模块,出库单物料规格管理
// @Accept json
// @Produce json
// @Security Bearer
// @Param request body v1.WmsDeliverSkuCreateParams true "出库单物料规格信息"
// @Success 200 {object} v1.Response
// @Router /wms/deliver-skus [post]
func (h *WmsDeliverSkuHandler) Create(ctx *gin.Context) {
	var req v1.WmsDeliverSkuCreateParams
	if err := ctx.ShouldBindJSON(&req); err != nil {
		v1.HandleError(ctx, http.StatusBadRequest, v1.ErrBadRequest, nil)
		return
	}

	if err := h.wmsDeliverSkuService.Create(ctx, &req); err != nil {
		v1.HandleError(ctx, http.StatusInternalServerError, err, nil)
		return
	}

	v1.HandleSuccess(ctx, nil)
}

// Update godoc
// @Summary 更新出库单物料规格
// @Schemes
// @Description 更新指定ID的出库单物料规格信息
// @Tags 仓储模块,出库单物料规格管理
// @Accept json
// @Produce json
// @Security Bearer
// @Param id path int true "出库单物料规格ID"
// @Param request body v1.WmsDeliverSkuUpdateParams true "出库单物料规格信息"
// @Success 200 {object} v1.Response
// @Router /wms/deliver-skus/{id} [patch]
func (h *WmsDeliverSkuHandler) Update(ctx *gin.Context) {
	idStr := ctx.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		v1.HandleError(ctx, http.StatusBadRequest, v1.ErrBadRequest, nil)
		return
	}

	var req v1.WmsDeliverSkuUpdateParams
	if err := ctx.ShouldBindJSON(&req); err != nil {
		v1.HandleError(ctx, http.StatusBadRequest, v1.ErrBadRequest, nil)
		return
	}

	if err := h.wmsDeliverSkuService.Update(ctx, uint(id), &req); err != nil {
		v1.HandleError(ctx, http.StatusInternalServerError, err, nil)
		return
	}

	v1.HandleSuccess(ctx, nil)
}

// Delete godoc
// @Summary 删除出库单物料规格
// @Schemes
// @Description 删除指定ID的出库单物料规格
// @Tags 仓储模块,出库单物料规格管理
// @Accept json
// @Produce json
// @Security Bearer
// @Param id path int true "出库单物料规格ID"
// @Success 200 {object} v1.Response
// @Router /wms/deliver-skus/{id} [delete]
func (h *WmsDeliverSkuHandler) Delete(ctx *gin.Context) {
	idStr := ctx.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		v1.HandleError(ctx, http.StatusBadRequest, v1.ErrBadRequest, nil)
		return
	}

	if err := h.wmsDeliverSkuService.Delete(ctx, uint(id)); err != nil {
		v1.HandleError(ctx, http.StatusInternalServerError, err, nil)
		return
	}

	v1.HandleSuccess(ctx, nil)
}

// BatchDelete godoc
// @Summary 批量删除出库单物料规格
// @Schemes
// @Description 批量删除指定IDs的出库单物料规格
// @Tags 仓储模块,出库单物料规格管理
// @Accept json
// @Produce json
// @Security Bearer
// @Param request body v1.BatchDeleteParams true "出库单物料规格IDs"
// @Success 200 {object} v1.Response
// @Router /wms/deliver-skus [delete]
func (h *WmsDeliverSkuHandler) BatchDelete(ctx *gin.Context) {
	var req v1.BatchDeleteParams
	if err := ctx.ShouldBindJSON(&req); err != nil {
		v1.HandleError(ctx, http.StatusBadRequest, v1.ErrBadRequest, nil)
		return
	}

	if err := h.wmsDeliverSkuService.BatchDelete(ctx, req.IDs); err != nil {
		v1.HandleError(ctx, http.StatusInternalServerError, err, nil)
		return
	}

	v1.HandleSuccess(ctx, nil)
}

// Get godoc
// @Summary 获取出库单物料规格
// @Schemes
// @Description 获取指定ID的出库单物料规格信息
// @Tags 仓储模块,出库单物料规格管理
// @Accept json
// @Produce json
// @Security Bearer
// @Param id path int true "出库单物料规格ID"
// @Param _expand query string false "展开关联信息，支持: deliver,item,sku" example:"deliver,item,sku"
// @Success 200 {object} v1.Response{data=v1.WmsDeliverSkuResponse}
// @Router /wms/deliver-skus/{id} [get]
func (h *WmsDeliverSkuHandler) Get(ctx *gin.Context) {
	idStr := ctx.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		v1.HandleError(ctx, http.StatusBadRequest, v1.ErrBadRequest, nil)
		return
	}

	deliverSku, err := h.wmsDeliverSkuService.Get(ctx, uint(id), strings.Split(ctx.Query("_expand"), ","))
	if err != nil {
		v1.HandleError(ctx, http.StatusInternalServerError, err, nil)
		return
	}

	v1.HandleSuccess(ctx, deliverSku)
}

// List godoc
// @Summary 获取出库单物料规格列表
// @Schemes
// @Description 分页获取出库单物料规格列表
// @Tags 仓储模块,出库单物料规格管理
// @Accept json
// @Produce json
// @Security Bearer
// @Param _page query int false "当前页码(默认值: 1)" example:"1"
// @Param _limit query int false "每页数量(默认值: 10)" example:"10"
// @Param _sort query string false "排序字段，多个字段用逗号分隔" example:"id"
// @Param _order query string false "排序方式，asc或desc，多个方式用逗号分隔" example:"desc"
// @Param q query string false "全文搜索" example:"关键词"
// @Param status query bool false "状态筛选" example:"true"
// @Param deliverId query int false "出库单ID筛选" example:"1"
// @Param itemId query int false "物料ID筛选" example:"1"
// @Param skuId query int false "规格ID筛选" example:"1"
// @Param _expand query string false "展开关联信息，支持: deliver,item,sku" example:"deliver,item,sku"
// @Success 200 {object} v1.Response{data=pagination.Result{records=[]v1.WmsDeliverSkuResponse}}
// @Router /wms/deliver-skus [get]
func (h *WmsDeliverSkuHandler) List(ctx *gin.Context) {
	var params pagination.Params
	if err := ctx.ShouldBindQuery(&params); err != nil {
		v1.HandleError(ctx, http.StatusBadRequest, v1.ErrBadRequest, nil)
		return
	}

	// 状态筛选
	if status := ctx.DefaultQuery("status", ""); status != "" {
		params.AddFilter("status", status)
	}

	// 出库单ID筛选
	if deliverId := ctx.DefaultQuery("deliverId", ""); deliverId != "" {
		params.AddFilter("deliver_id", deliverId)
	}

	// 物料ID筛选
	if itemId := ctx.DefaultQuery("itemId", ""); itemId != "" {
		params.AddFilter("item_id", itemId)
	}

	// 规格ID筛选
	if skuId := ctx.DefaultQuery("skuId", ""); skuId != "" {
		params.AddFilter("sku_id", skuId)
	}

	result, err := h.wmsDeliverSkuService.List(ctx, &params, strings.Split(ctx.Query("_expand"), ","))
	if err != nil {
		v1.HandleError(ctx, http.StatusInternalServerError, err, nil)
		return
	}

	v1.HandleSuccess(ctx, result)
}
